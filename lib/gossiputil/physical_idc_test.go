package gossiputil

import (
	"testing"
)

// TestAnalysisPhysicalIDCLatency 测试物理IDC延迟分析功能
func TestAnalysisPhysicalIDCLatency(t *testing.T) {
	// 跳过这个测试，因为需要实际的Redis连接和配置
	t.<PERSON><PERSON>("Skipping TestAnalysisPhysicalIDCLatency - requires Redis connection and configuration")

	// 在实际环境中使用时的代码示例：
	// ctx := easy.NewContext()
	// err := AnalysisPhysicalIDCLatency(ctx)
	// if err != nil {
	//     t.Logf("AnalysisPhysicalIDCLatency returned error: %v", err)
	// } else {
	//     t.Log("AnalysisPhysicalIDCLatency completed successfully")
	// }
}

// TestCalculatePercentile 测试百分位计算函数
func TestCalculatePercentile(t *testing.T) {
	// 测试数据
	data := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}

	// 测试各种百分位
	tests := []struct {
		percentile float64
		expected   float64
	}{
		{0.50, 5.5},  // P50 (中位数)
		{0.80, 8.2},  // P80
		{0.90, 9.1},  // P90
		{0.95, 9.55}, // P95
		{0.99, 9.91}, // P99
		{1.0, 10.0},  // P100 (最大值)
	}

	for _, test := range tests {
		result := calculatePercentile(data, test.percentile)
		// 使用浮点数比较的容差
		tolerance := 0.01
		if result < test.expected-tolerance || result > test.expected+tolerance {
			t.Errorf("calculatePercentile(data, %.2f) = %.2f, expected %.2f",
				test.percentile, result, test.expected)
		}
	}
}

// TestGetGossipGroupNumByPhysicalIDC 测试获取物理IDC的gossip组数
func TestGetGossipGroupNumByPhysicalIDC(t *testing.T) {
	// 创建测试配置，包含所有逻辑IDC
	configMap := map[string]*Config{
		"yangquan": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"yq01": 10,
				"yq02": 8,
			},
			GroupNum: 20,
		},
		"xian": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"xaky": 15,
			},
			GroupNum: 20,
		},
		"beijing": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"bjhw": 12,
			},
			GroupNum: 20,
		},
	}

	// 测试用例
	tests := []struct {
		physicalIDC string
		expected    int
	}{
		{"yq01", 10}, // yangquan 逻辑IDC，有具体配置
		{"yq02", 8},  // yangquan 逻辑IDC，有具体配置
		{"xaky", 15}, // xian 逻辑IDC，有具体配置
		{"xakd", 20}, // xian 逻辑IDC，使用默认值
		{"bjhw", 12}, // beijing 逻辑IDC，有具体配置
		{"bdjl", 20}, // beijing 逻辑IDC，使用默认值
	}

	for _, test := range tests {
		result := getGossipGroupNumByPhysicalIDC(test.physicalIDC, configMap, nil)
		if result != test.expected {
			t.Errorf("getGossipGroupNumByPhysicalIDC(%s) = %d, expected %d",
				test.physicalIDC, result, test.expected)
		}
	}
}

// TestEnhancedGossipAnalysis 测试增强的gossip分析功能
func TestEnhancedGossipAnalysis(t *testing.T) {
	// 跳过这个测试，因为需要实际的Redis连接和配置
	t.Skip("Skipping TestEnhancedGossipAnalysis - requires Redis connection and configuration")

	// 在实际环境中使用时的代码示例：
	// ctx := easy.NewContext()
	// err := AnalysisLongTailResult(ctx, 100)
	// if err != nil {
	//     t.Logf("Enhanced gossip analysis returned error: %v", err)
	// } else {
	//     t.Log("Enhanced gossip analysis completed successfully")
	// }
}

// TestCalculateCorrelation 测试相关系数计算
func TestCalculateCorrelation(t *testing.T) {
	tests := []struct {
		name      string
		x         []float64
		y         []float64
		expected  float64
		tolerance float64
	}{
		{
			name:      "perfect positive correlation",
			x:         []float64{1, 2, 3, 4, 5},
			y:         []float64{2, 4, 6, 8, 10},
			expected:  1.0,
			tolerance: 0.001,
		},
		{
			name:      "perfect negative correlation",
			x:         []float64{1, 2, 3, 4, 5},
			y:         []float64{10, 8, 6, 4, 2},
			expected:  -1.0,
			tolerance: 0.001,
		},
		{
			name:      "no correlation",
			x:         []float64{1, 2, 3, 4, 5},
			y:         []float64{3, 3, 3, 3, 3},
			expected:  0.0,
			tolerance: 0.001,
		},
		{
			name:      "moderate positive correlation",
			x:         []float64{1, 2, 3, 4, 5, 6},
			y:         []float64{1, 3, 2, 5, 4, 6},
			expected:  0.8,
			tolerance: 0.2,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := calculateCorrelation(test.x, test.y)
			if abs(result-test.expected) > test.tolerance {
				t.Errorf("calculateCorrelation(%v, %v) = %f, expected %f (tolerance %f)",
					test.x, test.y, result, test.expected, test.tolerance)
			}
		})
	}
}

// abs 计算浮点数的绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// TestAnalyzeGossipIndexDistribution 测试gossip索引分布分析
func TestAnalyzeGossipIndexDistribution(t *testing.T) {
	// 创建测试数据
	analysis := &LongTailAnalysis{
		LogicIDC:           "test",
		TotalLongTailCount: 100,
		GossipGroupDistrib: map[string]*GossipGroupStats{
			"yq01-0": {
				GroupIndex:  0,
				PhysicalIDC: "yq01",
				Count:       30,
				AvgLatency:  1200.0,
			},
			"yq01-1": {
				GroupIndex:  1,
				PhysicalIDC: "yq01",
				Count:       20,
				AvgLatency:  1000.0,
			},
			"yq02-0": {
				GroupIndex:  0,
				PhysicalIDC: "yq02",
				Count:       25,
				AvgLatency:  1100.0,
			},
			"yq02-1": {
				GroupIndex:  1,
				PhysicalIDC: "yq02",
				Count:       15,
				AvgLatency:  900.0,
			},
			"yq02-2": {
				GroupIndex:  2,
				PhysicalIDC: "yq02",
				Count:       10,
				AvgLatency:  800.0,
			},
		},
	}

	// 这里只是验证函数不会panic，实际的输出需要在有Redis连接的环境中测试
	t.Log("Testing gossip index distribution analysis with mock data")

	// 验证数据结构的正确性
	if analysis.TotalLongTailCount != 100 {
		t.Errorf("Expected total long tail count 100, got %d", analysis.TotalLongTailCount)
	}

	if len(analysis.GossipGroupDistrib) != 5 {
		t.Errorf("Expected 5 gossip groups, got %d", len(analysis.GossipGroupDistrib))
	}

	// 验证索引0的数据
	index0Count := 0
	for _, stats := range analysis.GossipGroupDistrib {
		if stats.GroupIndex == 0 {
			index0Count += stats.Count
		}
	}

	expectedIndex0Count := 30 + 25 // yq01-0 + yq02-0
	if index0Count != expectedIndex0Count {
		t.Errorf("Expected index 0 count %d, got %d", expectedIndex0Count, index0Count)
	}

	t.Log("Gossip index distribution analysis test completed")
}
