package gossiputil

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

var idc = []string{
	"bjhw",
	"gajl",
	"yq01",
	"bdjl",
	"xafj",
	"xakd",
	"yq02",
	"bddwd",
	"bddx",
	"bjyz",
	"xaky",
}

var idcCounts = map[string]int{
	"bjhw":  740,
	"gajl":  125,
	"yq01":  8759,
	"bdjl":  3746,
	"xafj":  5340,
	"xakd":  3114,
	"yq02":  3638,
	"bddwd": 2111,
	"bddx":  1983,
	"bjyz":  971,
	"xaky":  4317,
}

type Config struct {
	Enable      bool           `json:"enable"`
	IDCGroupNum map[string]int `json:"idc_group_num"`
	Rate        float64        `json:"rate"`
	GroupNum    int            `json:"group_num"`
}

// PhysicalIDCLatencyStats 物理IDC延迟统计结构
type PhysicalIDCLatencyStats struct {
	PhysicalIDC          string  `json:"physical_idc"`           // 物理机房名称，如 yq01, yq02, xaky 等
	MetricType           string  `json:"metric_type"`            // 指标类型：worker_latency, privileged_latency, agent_latency, total_latency
	P100                 float64 `json:"p100"`                   // P100 百分位（最大值）
	P99                  float64 `json:"p99"`                    // P99 百分位
	P95                  float64 `json:"p95"`                    // P95 百分位
	P90                  float64 `json:"p90"`                    // P90 百分位
	P80                  float64 `json:"p80"`                    // P80 百分位
	Median               float64 `json:"median"`                 // 中位数
	TrimmedMean          float64 `json:"trimmed_mean"`           // 去除最大最小各5个机器的平均数
	TotalCount           int     `json:"total_count"`            // 总机器数
	ExceedThresholdCount int     `json:"exceed_threshold_count"` // 超过阈值的机器数
	ThresholdValue       int64   `json:"threshold_value"`        // 阈值
	GossipGroupNum       int     `json:"gossip_group_num"`       // 该物理IDC的gossip组数
	AvgMachineNum        int     `json:"avg_machine_num"`        // 平均每组机器数
	Timestamp            int64   `json:"timestamp"`              // 统计时间戳
}

// LongTailAnalysis 长尾分析结构
type LongTailAnalysis struct {
	LogicIDC               string                       `json:"logic_idc"`               // 逻辑机房名称
	TotalLongTailCount     int                          `json:"total_long_tail_count"`   // 长尾总数
	PhysicalIDCDistrib     map[string]*PhysicalIDCStats `json:"physical_idc_distrib"`    // 按物理IDC分布
	GossipGroupDistrib     map[string]*GossipGroupStats `json:"gossip_group_distrib"`    // 按gossip组分布
	LatencyCharacteristics *LatencyCharacteristics      `json:"latency_characteristics"` // 延迟特征
	CurrentConfig          *Config                      `json:"current_config"`          // 当前配置
}

// PhysicalIDCStats 物理IDC统计
type PhysicalIDCStats struct {
	PhysicalIDC  string   `json:"physical_idc"`  // 物理IDC名称
	Count        int      `json:"count"`         // 长尾数量
	Percentage   float64  `json:"percentage"`    // 占比
	AvgLatency   float64  `json:"avg_latency"`   // 平均延迟
	MaxLatency   float64  `json:"max_latency"`   // 最大延迟
	IPs          []string `json:"ips"`           // 涉及的IP列表
	GossipGroups []int    `json:"gossip_groups"` // 涉及的gossip组
}

// GossipGroupStats gossip组统计
type GossipGroupStats struct {
	GroupIndex  int      `json:"group_index"`  // 组索引
	PhysicalIDC string   `json:"physical_idc"` // 物理IDC
	Count       int      `json:"count"`        // 长尾数量
	Percentage  float64  `json:"percentage"`   // 占比
	AvgLatency  float64  `json:"avg_latency"`  // 平均延迟
	MaxLatency  float64  `json:"max_latency"`  // 最大延迟
	IPs         []string `json:"ips"`          // 涉及的IP列表
	GroupSize   int      `json:"group_size"`   // 组大小
}

// LatencyCharacteristics 延迟特征
type LatencyCharacteristics struct {
	MinLatency    float64 `json:"min_latency"`    // 最小延迟
	MaxLatency    float64 `json:"max_latency"`    // 最大延迟
	AvgLatency    float64 `json:"avg_latency"`    // 平均延迟
	MedianLatency float64 `json:"median_latency"` // 中位数延迟
	P95Latency    float64 `json:"p95_latency"`    // P95延迟
	P99Latency    float64 `json:"p99_latency"`    // P99延迟
	StdDeviation  float64 `json:"std_deviation"`  // 标准差
}

// OptimizationSuggestion 优化建议
type OptimizationSuggestion struct {
	LogicIDC            string         `json:"logic_idc"`            // 逻辑机房
	CurrentGroupNum     int            `json:"current_group_num"`    // 当前组数
	SuggestedGroupNum   int            `json:"suggested_group_num"`  // 建议组数
	Reason              string         `json:"reason"`               // 建议原因
	ExpectedImprovement string         `json:"expected_improvement"` // 预期改善
	PhysicalIDCChanges  map[string]int `json:"physical_idc_changes"` // 各物理IDC的组数变化
	RiskAssessment      string         `json:"risk_assessment"`      // 风险评估
}

var defaultConfig = Config{
	Enable: true,
	IDCGroupNum: map[string]int{
		"default": 20,
		"gzhxy":   1,
		"bddwd":   10,
		"bddx":    7,
		"bjyz":    9,
		"st01":    3,
		"bjhw":    7,
	},
	Rate:     0.1,
	GroupNum: 20,
}

// Analaysis 函数用于执行分析操作
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func Analaysis(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")

	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("analysis err").SetErr(err).Print()
		return err
	}

	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 对于每个物理 idc 内，需要监控其 gossip 分组情况
	for phyIdc, _ := range instanceInfo.InstanceInfo {
		// 分析每个 idc 内部分组情况
		ufclogidcIdc := netutil.GetIDCByHostname(phyIdc)
		gossipConfig, ok := configMap[ufclogidcIdc]
		if !ok {
			continue
		}
		groupNum := getGroupNumByIDC(*gossipConfig, phyIdc)
		for i := 0; i < groupNum; i++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", ufclogidcIdc, phyIdc, i)
			scardCnt, err := redis.Scard(key)
			if err != nil {
				ctx.SLog.Warning("get scard error").SetErr(err).Print()
				return err
			}

			ctx.SLog.Info("idc gossip group info").
				Set("idc", phyIdc).
				Set("idc_instance_cnt", instanceInfo.InstanceCnt[phyIdc]).
				Set("redis_key", key).
				Set("group_num", groupNum).
				Set("group_index", i).
				Set("scard_cnt", scardCnt).
				Print()
		}

	}

	return nil
}

func getGroupNumByIDC(gossipConfig Config, idc string) int {
	num, ok := gossipConfig.IDCGroupNum[idc]
	if !ok {
		num = gossipConfig.GroupNum
	}
	return num

}

func getGossipConfigMap(redis *easy.Redis) (map[string]*Config, error) {
	idcs := []string{
		"beijing",
		"yangquan",
		"xian",
	}

	configMap := make(map[string]*Config)
	for _, idc := range idcs {
		config, err := getGossipConfigByIDC(redis, idc)
		if err != nil {
			return nil, err
		}
		configMap[idc] = config
	}
	return configMap, nil
}
func getGossipConfigByIDC(redis *easy.Redis, idc string) (*Config, error) {
	key := fmt.Sprintf("p-3ufc-%s-config-gossip", idc)
	value, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var config Config

	err = json.Unmarshal([]byte(value), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}
	return &config, nil
}

// AnalysisPhysicalIDCLatency 分析每个物理IDC的延迟情况
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func AnalysisPhysicalIDCLatency(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")
	timestamp := time.Now().Unix()

	ctx.SLog.Info("Starting physical IDC latency analysis").Print()

	// 获取gossip配置信息
	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("get gossip config map error").SetErr(err).Print()
		return err
	}

	// 获取实例信息
	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 统计各种延迟类型
	metricTypes := []struct {
		name      string
		zsetKey   string
		threshold int64
	}{
		{"worker_latency", cacheutil.GetUFCWorkerLatencyZSetName(), 60},
		{"privileged_latency", cacheutil.GetUFCPrivilegedLatencyZSetName(), 60},
		{"total_latency", cacheutil.GetUFCTotalLatencyZSetName(), 120},
	}

	for _, metric := range metricTypes {
		ctx.SLog.Info("Processing metric type").Set("metric_type", metric.name).Print()

		stats, err := calculatePhysicalIDCLatencyStats(ctx, redis, metric.name, metric.zsetKey, metric.threshold, configMap, instanceInfo, timestamp)
		if err != nil {
			ctx.SLog.Warning("calculate physical IDC latency stats error").Set("metric_type", metric.name).SetErr(err).Print()
			continue
		}

		// 输出统计结果
		for _, stat := range stats {
			ctx.SLog.Info("physical IDC latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("avg_machine_num", stat.AvgMachineNum).
				Set("median", stat.Median).
				Set("trimmed_mean", stat.TrimmedMean).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	// 处理 Agent 延迟统计
	ctx.SLog.Info("Processing agent latency").Print()
	agentStats, err := calculatePhysicalIDCAgentLatencyStats(ctx, redis, cacheutil.GetUFCIPMtimesZSetName(), 300, configMap, instanceInfo, timestamp)
	if err != nil {
		ctx.SLog.Warning("calculate physical IDC agent latency stats error").SetErr(err).Print()
	} else {
		for _, stat := range agentStats {
			ctx.SLog.Info("physical IDC agent latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	ctx.SLog.Info("Physical IDC latency analysis completed").Print()
	return nil
}

// calculatePhysicalIDCLatencyStats 计算物理IDC延迟统计
func calculatePhysicalIDCLatencyStats(ctx *easy.Context, redis *easy.Redis, metricType, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, timestamp int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		latency := conv.ToFloat64(slice[2*i+1])

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], latency)
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           metricType,
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,

			Timestamp: timestamp,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePhysicalIDCAgentLatencyStats 计算物理IDC Agent延迟统计（mtime 到当前时间的差值）
func calculatePhysicalIDCAgentLatencyStats(ctx *easy.Context, redis *easy.Redis, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, currentTime int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		mtime := conv.ToInt64(slice[2*i+1])

		if mtime <= 0 {
			// 跳过无效的 mtime
			continue
		}

		latency := currentTime - mtime
		if latency < 0 {
			latency = 0
		}

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], float64(latency))
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           "agent_latency",
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,
			Timestamp:            currentTime,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePercentile 计算百分位数
func calculatePercentile(sortedData []float64, percentile float64) float64 {
	if len(sortedData) == 0 {
		return 0
	}

	if len(sortedData) == 1 {
		return sortedData[0]
	}

	// 计算索引位置
	index := percentile * float64(len(sortedData)-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if upper >= len(sortedData) {
		upper = len(sortedData) - 1
	}
	if lower < 0 {
		lower = 0
	}

	if lower == upper {
		return sortedData[lower]
	}

	// 线性插值
	weight := index - float64(lower)
	return sortedData[lower]*(1-weight) + sortedData[upper]*weight
}

// 分析每个机房长尾数据，结合gossip协议和物理IDC延迟分析
func AnalysisLongTailResult(ctx *easy.Context, tailCnt int) error {
	// zrange p-6ufc-moniter-total-latency-yangquan-zset -100 -1 WITHSCORES [WITH
	logicIdcs := []string{"beijing", "yangquan", "xian"}
	redis := easy.NewRedis(ctx, "redis")

	ctx.SLog.Info("=== Starting Enhanced Long Tail Analysis ===").
		Set("tail_count", tailCnt).
		Set("logic_idcs", logicIdcs).Print()

	// 首先执行物理IDC延迟分析，获取基准数据
	ctx.SLog.Info("Executing physical IDC latency analysis for baseline data").Print()
	err := AnalysisPhysicalIDCLatency(ctx)
	if err != nil {
		ctx.SLog.Warning("physical IDC latency analysis failed, continuing with long tail analysis").SetErr(err).Print()
	}

	// 获取gossip配置信息用于全局分析
	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("get gossip config map error").SetErr(err).Print()
		return err
	}

	// 执行每个逻辑IDC的长尾分析
	for _, logicIdc := range logicIdcs {
		ctx.SLog.Info("analyzing long tail for logic IDC").Set("logic_idc", logicIdc).Print()
		analysisLongTailResultByIDC(ctx, redis, logicIdc, tailCnt, configMap)
	}

	// 执行跨IDC的对比分析
	performCrossIDCAnalysis(ctx, redis, logicIdcs, tailCnt, configMap)

	return nil
}

func analysisLongTailResultByIDC(ctx *easy.Context, redis *easy.Redis, logicIDC string, tailCnt int, configMap ...map[string]*Config) {
	key := fmt.Sprintf("p-6ufc-moniter-total-latency-%s-zset", logicIDC)
	slice, err := redis.ZRangeWithScores(key, -tailCnt, -1)
	if err != nil {
		ctx.SLog.Warning("get long tail data error").Set("key", key).SetErr(err).Print()
		return
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no long tail data").Set("key", key).Print()
		return
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("invalid long tail data length").Set("key", key).Set("len", len(slice)).Print()
		return
	}

	ctx.SLog.Info("analyzing long tail data").Set("logic_idc", logicIDC).Set("tail_count", tailCnt).Print()

	// 获取gossip配置
	gossipConfig, err := getGossipConfigByIDC(redis, logicIDC)
	if err != nil {
		ctx.SLog.Warning("get gossip config error").Set("logic_idc", logicIDC).SetErr(err).Print()
		return
	}

	// 分析长尾数据的特征
	longTailAnalysis := analyzeLongTailCharacteristics(ctx, slice, logicIDC, gossipConfig)

	// 执行增强的gossip协议相关分析
	performEnhancedGossipAnalysis(ctx, redis, longTailAnalysis, gossipConfig)

	// 分析长尾机器在gossip组索引上的分布
	analyzeGossipIndexDistribution(ctx, redis, longTailAnalysis, gossipConfig)

	// 分析不同gossip组之间的延迟差异
	analyzeGossipGroupLatencyDifferences(ctx, redis, longTailAnalysis, gossipConfig)

	// 如果有全局配置，执行与物理IDC延迟的关联分析
	if len(configMap) > 0 && configMap[0] != nil {
		analyzeCorrelationWithPhysicalIDCLatency(ctx, redis, longTailAnalysis, configMap[0])
	}

	// 输出分析结果
	outputLongTailAnalysis(ctx, longTailAnalysis)

	// 提供优化建议
	optimizationSuggestions := generateOptimizationSuggestions(ctx, longTailAnalysis)
	outputOptimizationSuggestions(ctx, optimizationSuggestions)
}

// getGossipGroupNumByPhysicalIDC 获取物理IDC的gossip组数
func getGossipGroupNumByPhysicalIDC(physicalIDC string, configMap map[string]*Config, _ *common.InstanceInfo) int {
	// 首先通过物理IDC获取逻辑IDC
	logicalIDC := netutil.GetIDCByHostname(physicalIDC + ".baidu.com")

	// 获取该逻辑IDC的gossip配置
	gossipConfig, ok := configMap[logicalIDC]
	if !ok {
		// 如果没有配置，返回默认值
		return defaultConfig.GroupNum
	}

	// 获取该物理IDC的组数配置
	groupNum := getGroupNumByIDC(*gossipConfig, physicalIDC)
	return groupNum
}

// analyzeLongTailCharacteristics 分析长尾数据特征
func analyzeLongTailCharacteristics(ctx *easy.Context, slice []string, logicIDC string, gossipConfig *Config) *LongTailAnalysis {
	totalCount := len(slice) / 2
	if totalCount == 0 {
		return nil
	}

	analysis := &LongTailAnalysis{
		LogicIDC:           logicIDC,
		TotalLongTailCount: totalCount,
		PhysicalIDCDistrib: make(map[string]*PhysicalIDCStats),
		GossipGroupDistrib: make(map[string]*GossipGroupStats),
		CurrentConfig:      gossipConfig,
	}

	// 收集所有延迟数据和IP信息
	var latencies []float64
	ipLatencyMap := make(map[string]float64)

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		latency := conv.ToFloat64(slice[2*i+1])

		latencies = append(latencies, latency)
		ipLatencyMap[ip] = latency
	}

	// 计算延迟特征
	analysis.LatencyCharacteristics = calculateLatencyCharacteristics(latencies)

	// 按物理IDC分组分析
	analyzeByPhysicalIDC(ctx, analysis, ipLatencyMap)

	// 按gossip组分析
	analyzeByGossipGroup(ctx, analysis, ipLatencyMap, gossipConfig)

	return analysis
}

// performEnhancedGossipAnalysis 执行增强的gossip协议相关分析
func performEnhancedGossipAnalysis(ctx *easy.Context, redis *easy.Redis, analysis *LongTailAnalysis, config *Config) {
	if analysis == nil {
		return
	}

	ctx.SLog.Info("=== Enhanced Gossip Protocol Analysis ===").Set("logic_idc", analysis.LogicIDC).Print()

	// 分析gossip组的健康度
	analyzeGossipGroupHealth(ctx, redis, analysis, config)

	// 分析gossip组的负载均衡情况
	analyzeGossipGroupLoadBalance(ctx, analysis, config)

	// 分析gossip组的延迟分布模式
	analyzeGossipGroupLatencyPattern(ctx, analysis)
}

// analyzeGossipGroupHealth 分析gossip组的健康度
func analyzeGossipGroupHealth(ctx *easy.Context, redis *easy.Redis, analysis *LongTailAnalysis, config *Config) {
	ctx.SLog.Info("--- Gossip Group Health Analysis ---").Set("logic_idc", analysis.LogicIDC).Print()

	totalGroups := 0
	healthyGroups := 0
	problematicGroups := 0

	for groupKey, stats := range analysis.GossipGroupDistrib {
		totalGroups++

		// 计算长尾比例
		longTailRatio := float64(stats.Count) / float64(stats.GroupSize)

		// 定义健康阈值：长尾比例小于10%认为是健康的
		healthThreshold := 0.1

		if longTailRatio < healthThreshold {
			healthyGroups++
			ctx.SLog.Info("healthy gossip group").
				Set("group_key", groupKey).
				Set("physical_idc", stats.PhysicalIDC).
				Set("group_index", stats.GroupIndex).
				Set("long_tail_ratio", fmt.Sprintf("%.2f%%", longTailRatio*100)).
				Set("group_size", stats.GroupSize).
				Set("avg_latency", stats.AvgLatency).Print()
		} else {
			problematicGroups++
			ctx.SLog.Warning("problematic gossip group").
				Set("group_key", groupKey).
				Set("physical_idc", stats.PhysicalIDC).
				Set("group_index", stats.GroupIndex).
				Set("long_tail_ratio", fmt.Sprintf("%.2f%%", longTailRatio*100)).
				Set("group_size", stats.GroupSize).
				Set("avg_latency", stats.AvgLatency).
				Set("max_latency", stats.MaxLatency).Print()
		}
	}

	healthRate := float64(healthyGroups) / float64(totalGroups) * 100
	ctx.SLog.Info("gossip group health summary").
		Set("logic_idc", analysis.LogicIDC).
		Set("total_groups", totalGroups).
		Set("healthy_groups", healthyGroups).
		Set("problematic_groups", problematicGroups).
		Set("health_rate", fmt.Sprintf("%.2f%%", healthRate)).Print()
}

// analyzeGossipGroupLoadBalance 分析gossip组的负载均衡情况
func analyzeGossipGroupLoadBalance(ctx *easy.Context, analysis *LongTailAnalysis, config *Config) {
	ctx.SLog.Info("--- Gossip Group Load Balance Analysis ---").Set("logic_idc", analysis.LogicIDC).Print()

	// 按物理IDC分组统计
	physicalIDCGroupStats := make(map[string][]int) // 物理IDC -> 各组的长尾数量

	for _, stats := range analysis.GossipGroupDistrib {
		physicalIDCGroupStats[stats.PhysicalIDC] = append(physicalIDCGroupStats[stats.PhysicalIDC], stats.Count)
	}

	// 分析每个物理IDC内部的负载均衡
	for physicalIDC, groupCounts := range physicalIDCGroupStats {
		if len(groupCounts) == 0 {
			continue
		}

		// 计算标准差来衡量负载均衡程度
		mean := 0.0
		for _, count := range groupCounts {
			mean += float64(count)
		}
		mean /= float64(len(groupCounts))

		variance := 0.0
		for _, count := range groupCounts {
			variance += math.Pow(float64(count)-mean, 2)
		}
		variance /= float64(len(groupCounts))
		stdDev := math.Sqrt(variance)

		// 计算变异系数（标准差/均值）
		coefficientOfVariation := stdDev / mean

		balanceLevel := "excellent"
		if coefficientOfVariation > 0.5 {
			balanceLevel = "poor"
		} else if coefficientOfVariation > 0.3 {
			balanceLevel = "moderate"
		} else if coefficientOfVariation > 0.1 {
			balanceLevel = "good"
		}

		ctx.SLog.Info("physical IDC load balance analysis").
			Set("physical_idc", physicalIDC).
			Set("group_count", len(groupCounts)).
			Set("mean_long_tail", fmt.Sprintf("%.2f", mean)).
			Set("std_deviation", fmt.Sprintf("%.2f", stdDev)).
			Set("coefficient_of_variation", fmt.Sprintf("%.3f", coefficientOfVariation)).
			Set("balance_level", balanceLevel).Print()
	}
}

// analyzeGossipGroupLatencyPattern 分析gossip组的延迟分布模式
func analyzeGossipGroupLatencyPattern(ctx *easy.Context, analysis *LongTailAnalysis) {
	ctx.SLog.Info("--- Gossip Group Latency Pattern Analysis ---").Set("logic_idc", analysis.LogicIDC).Print()

	if len(analysis.GossipGroupDistrib) == 0 {
		ctx.SLog.Info("no gossip group data for latency pattern analysis").Print()
		return
	}

	// 收集所有组的延迟数据
	var allGroupAvgLatencies []float64
	var allGroupMaxLatencies []float64

	for _, stats := range analysis.GossipGroupDistrib {
		allGroupAvgLatencies = append(allGroupAvgLatencies, stats.AvgLatency)
		allGroupMaxLatencies = append(allGroupMaxLatencies, stats.MaxLatency)
	}

	// 计算延迟分布统计
	sort.Float64s(allGroupAvgLatencies)
	sort.Float64s(allGroupMaxLatencies)

	avgLatencyP50 := allGroupAvgLatencies[len(allGroupAvgLatencies)/2]
	avgLatencyP95 := allGroupAvgLatencies[int(float64(len(allGroupAvgLatencies))*0.95)]

	maxLatencyP50 := allGroupMaxLatencies[len(allGroupMaxLatencies)/2]
	maxLatencyP95 := allGroupMaxLatencies[int(float64(len(allGroupMaxLatencies))*0.95)]

	ctx.SLog.Info("gossip group latency distribution").
		Set("logic_idc", analysis.LogicIDC).
		Set("total_groups", len(analysis.GossipGroupDistrib)).
		Set("avg_latency_p50", avgLatencyP50).
		Set("avg_latency_p95", avgLatencyP95).
		Set("max_latency_p50", maxLatencyP50).
		Set("max_latency_p95", maxLatencyP95).Print()

	// 识别异常组
	threshold := avgLatencyP95 * 1.2 // 超过P95的1.2倍认为是异常
	for groupKey, stats := range analysis.GossipGroupDistrib {
		if stats.AvgLatency > threshold {
			ctx.SLog.Warning("abnormal latency gossip group").
				Set("group_key", groupKey).
				Set("physical_idc", stats.PhysicalIDC).
				Set("group_index", stats.GroupIndex).
				Set("avg_latency", stats.AvgLatency).
				Set("threshold", threshold).
				Set("deviation_ratio", fmt.Sprintf("%.2f", stats.AvgLatency/threshold)).Print()
		}
	}
}

// analyzeGossipIndexDistribution 分析长尾机器在gossip组索引上的分布
func analyzeGossipIndexDistribution(ctx *easy.Context, redis *easy.Redis, analysis *LongTailAnalysis, config *Config) {
	ctx.SLog.Info("=== Gossip Index Distribution Analysis ===").Set("logic_idc", analysis.LogicIDC).Print()

	// 统计每个索引的长尾数量
	indexDistribution := make(map[int]int)            // 索引 -> 长尾数量
	indexLatencySum := make(map[int]float64)          // 索引 -> 延迟总和
	indexLatencyCount := make(map[int]int)            // 索引 -> 延迟计数
	indexPhysicalIDCs := make(map[int]map[string]int) // 索引 -> 物理IDC -> 数量

	for _, stats := range analysis.GossipGroupDistrib {
		index := stats.GroupIndex
		indexDistribution[index] += stats.Count
		indexLatencySum[index] += stats.AvgLatency * float64(stats.Count)
		indexLatencyCount[index] += stats.Count

		if indexPhysicalIDCs[index] == nil {
			indexPhysicalIDCs[index] = make(map[string]int)
		}
		indexPhysicalIDCs[index][stats.PhysicalIDC] += stats.Count
	}

	// 分析索引分布特征
	ctx.SLog.Info("--- Index Distribution Summary ---").Print()

	var indices []int
	for index := range indexDistribution {
		indices = append(indices, index)
	}
	sort.Ints(indices)

	totalLongTail := analysis.TotalLongTailCount
	for _, index := range indices {
		count := indexDistribution[index]
		percentage := float64(count) / float64(totalLongTail) * 100
		avgLatency := indexLatencySum[index] / float64(indexLatencyCount[index])

		// 统计该索引涉及的物理IDC
		physicalIDCCount := len(indexPhysicalIDCs[index])

		ctx.SLog.Info("gossip index distribution").
			Set("index", index).
			Set("long_tail_count", count).
			Set("percentage", fmt.Sprintf("%.2f%%", percentage)).
			Set("avg_latency", fmt.Sprintf("%.2f", avgLatency)).
			Set("physical_idc_count", physicalIDCCount).Print()

		// 如果某个索引的长尾比例过高，标记为问题索引
		if percentage > 20.0 { // 超过20%认为是问题索引
			ctx.SLog.Warning("problematic gossip index detected").
				Set("index", index).
				Set("percentage", fmt.Sprintf("%.2f%%", percentage)).
				Set("avg_latency", fmt.Sprintf("%.2f", avgLatency)).Print()

			// 详细分析该索引的物理IDC分布
			ctx.SLog.Info("problematic index physical IDC breakdown").Set("index", index).Print()
			for physicalIDC, idcCount := range indexPhysicalIDCs[index] {
				idcPercentage := float64(idcCount) / float64(count) * 100
				ctx.SLog.Info("index physical IDC distribution").
					Set("index", index).
					Set("physical_idc", physicalIDC).
					Set("count", idcCount).
					Set("percentage_in_index", fmt.Sprintf("%.2f%%", idcPercentage)).Print()
			}
		}
	}
}

// analyzeGossipGroupLatencyDifferences 分析不同gossip组之间的延迟差异
func analyzeGossipGroupLatencyDifferences(ctx *easy.Context, redis *easy.Redis, analysis *LongTailAnalysis, config *Config) {
	ctx.SLog.Info("=== Gossip Group Latency Differences Analysis ===").Set("logic_idc", analysis.LogicIDC).Print()

	if len(analysis.GossipGroupDistrib) < 2 {
		ctx.SLog.Info("insufficient gossip groups for latency difference analysis").Print()
		return
	}

	// 按物理IDC分组分析
	physicalIDCGroups := make(map[string][]*GossipGroupStats)
	for _, stats := range analysis.GossipGroupDistrib {
		physicalIDCGroups[stats.PhysicalIDC] = append(physicalIDCGroups[stats.PhysicalIDC], stats)
	}

	// 分析每个物理IDC内部的组间差异
	for physicalIDC, groups := range physicalIDCGroups {
		if len(groups) < 2 {
			continue
		}

		ctx.SLog.Info("--- Physical IDC Group Latency Analysis ---").Set("physical_idc", physicalIDC).Print()

		// 计算延迟统计
		var avgLatencies []float64
		var maxLatencies []float64
		var groupSizes []int
		var longTailCounts []int

		for _, group := range groups {
			avgLatencies = append(avgLatencies, group.AvgLatency)
			maxLatencies = append(maxLatencies, group.MaxLatency)
			groupSizes = append(groupSizes, group.GroupSize)
			longTailCounts = append(longTailCounts, group.Count)
		}

		// 计算延迟差异
		sort.Float64s(avgLatencies)
		sort.Float64s(maxLatencies)

		minAvgLatency := avgLatencies[0]
		maxAvgLatency := avgLatencies[len(avgLatencies)-1]
		avgLatencyRange := maxAvgLatency - minAvgLatency

		minMaxLatency := maxLatencies[0]
		maxMaxLatency := maxLatencies[len(maxLatencies)-1]
		maxLatencyRange := maxMaxLatency - minMaxLatency

		// 计算平均值
		avgOfAvgLatencies := 0.0
		avgOfMaxLatencies := 0.0
		for i := range avgLatencies {
			avgOfAvgLatencies += avgLatencies[i]
			avgOfMaxLatencies += maxLatencies[i]
		}
		avgOfAvgLatencies /= float64(len(avgLatencies))
		avgOfMaxLatencies /= float64(len(maxLatencies))

		ctx.SLog.Info("physical IDC latency statistics").
			Set("physical_idc", physicalIDC).
			Set("group_count", len(groups)).
			Set("min_avg_latency", minAvgLatency).
			Set("max_avg_latency", maxAvgLatency).
			Set("avg_latency_range", avgLatencyRange).
			Set("avg_of_avg_latencies", avgOfAvgLatencies).
			Set("min_max_latency", minMaxLatency).
			Set("max_max_latency", maxMaxLatency).
			Set("max_latency_range", maxLatencyRange).
			Set("avg_of_max_latencies", avgOfMaxLatencies).Print()

		// 分析组大小与长尾数量的相关性
		analyzeGroupSizeCorrelation(ctx, physicalIDC, groups)

		// 识别异常组
		identifyAbnormalGroups(ctx, physicalIDC, groups, avgOfAvgLatencies)
	}

	// 跨物理IDC的对比分析
	analyzeCrossPhysicalIDCLatency(ctx, physicalIDCGroups)
}

// analyzeGroupSizeCorrelation 分析组大小与长尾数量的相关性
func analyzeGroupSizeCorrelation(ctx *easy.Context, physicalIDC string, groups []*GossipGroupStats) {
	if len(groups) < 3 {
		return
	}

	// 计算相关系数
	var groupSizes []float64
	var longTailRatios []float64

	for _, group := range groups {
		if group.GroupSize > 0 {
			groupSizes = append(groupSizes, float64(group.GroupSize))
			longTailRatios = append(longTailRatios, float64(group.Count)/float64(group.GroupSize))
		}
	}

	if len(groupSizes) < 3 {
		return
	}

	// 计算皮尔逊相关系数
	correlation := calculateCorrelation(groupSizes, longTailRatios)

	correlationLevel := "weak"
	if math.Abs(correlation) > 0.7 {
		correlationLevel = "strong"
	} else if math.Abs(correlation) > 0.4 {
		correlationLevel = "moderate"
	}

	ctx.SLog.Info("group size vs long tail ratio correlation").
		Set("physical_idc", physicalIDC).
		Set("correlation_coefficient", fmt.Sprintf("%.3f", correlation)).
		Set("correlation_level", correlationLevel).
		Set("sample_size", len(groupSizes)).Print()
}

// identifyAbnormalGroups 识别异常组
func identifyAbnormalGroups(ctx *easy.Context, physicalIDC string, groups []*GossipGroupStats, avgLatency float64) {
	threshold := avgLatency * 1.5 // 超过平均值1.5倍认为异常

	for _, group := range groups {
		if group.AvgLatency > threshold {
			longTailRatio := float64(group.Count) / float64(group.GroupSize) * 100

			ctx.SLog.Warning("abnormal gossip group detected").
				Set("physical_idc", physicalIDC).
				Set("group_index", group.GroupIndex).
				Set("avg_latency", group.AvgLatency).
				Set("threshold", threshold).
				Set("group_size", group.GroupSize).
				Set("long_tail_count", group.Count).
				Set("long_tail_ratio", fmt.Sprintf("%.2f%%", longTailRatio)).Print()
		}
	}
}

// analyzeCrossPhysicalIDCLatency 跨物理IDC的延迟对比分析
func analyzeCrossPhysicalIDCLatency(ctx *easy.Context, physicalIDCGroups map[string][]*GossipGroupStats) {
	ctx.SLog.Info("--- Cross Physical IDC Latency Comparison ---").Print()

	physicalIDCStats := make(map[string]map[string]float64)

	for physicalIDC, groups := range physicalIDCGroups {
		if len(groups) == 0 {
			continue
		}

		var avgLatencies []float64
		var maxLatencies []float64
		totalLongTail := 0
		totalMachines := 0

		for _, group := range groups {
			avgLatencies = append(avgLatencies, group.AvgLatency)
			maxLatencies = append(maxLatencies, group.MaxLatency)
			totalLongTail += group.Count
			totalMachines += group.GroupSize
		}

		// 计算统计量
		sort.Float64s(avgLatencies)
		sort.Float64s(maxLatencies)

		stats := make(map[string]float64)
		stats["min_avg_latency"] = avgLatencies[0]
		stats["max_avg_latency"] = avgLatencies[len(avgLatencies)-1]
		stats["median_avg_latency"] = avgLatencies[len(avgLatencies)/2]
		stats["min_max_latency"] = maxLatencies[0]
		stats["max_max_latency"] = maxLatencies[len(maxLatencies)-1]
		stats["median_max_latency"] = maxLatencies[len(maxLatencies)/2]
		stats["total_long_tail"] = float64(totalLongTail)
		stats["total_machines"] = float64(totalMachines)
		if totalMachines > 0 {
			stats["long_tail_ratio"] = float64(totalLongTail) / float64(totalMachines) * 100
		}

		physicalIDCStats[physicalIDC] = stats

		ctx.SLog.Info("physical IDC latency summary").
			Set("physical_idc", physicalIDC).
			Set("group_count", len(groups)).
			Set("min_avg_latency", stats["min_avg_latency"]).
			Set("max_avg_latency", stats["max_avg_latency"]).
			Set("median_avg_latency", stats["median_avg_latency"]).
			Set("total_long_tail", int(stats["total_long_tail"])).
			Set("total_machines", int(stats["total_machines"])).
			Set("long_tail_ratio", fmt.Sprintf("%.2f%%", stats["long_tail_ratio"])).Print()
	}

	// 找出最佳和最差的物理IDC
	var bestIDC, worstIDC string
	var bestRatio, worstRatio float64 = 100.0, 0.0

	for physicalIDC, stats := range physicalIDCStats {
		ratio := stats["long_tail_ratio"]
		if ratio < bestRatio {
			bestRatio = ratio
			bestIDC = physicalIDC
		}
		if ratio > worstRatio {
			worstRatio = ratio
			worstIDC = physicalIDC
		}
	}

	if bestIDC != "" && worstIDC != "" {
		ctx.SLog.Info("physical IDC performance ranking").
			Set("best_idc", bestIDC).
			Set("best_long_tail_ratio", fmt.Sprintf("%.2f%%", bestRatio)).
			Set("worst_idc", worstIDC).
			Set("worst_long_tail_ratio", fmt.Sprintf("%.2f%%", worstRatio)).
			Set("performance_gap", fmt.Sprintf("%.2f%%", worstRatio-bestRatio)).Print()
	}
}

// calculateCorrelation 计算皮尔逊相关系数
func calculateCorrelation(x, y []float64) float64 {
	if len(x) != len(y) || len(x) == 0 {
		return 0
	}

	n := float64(len(x))
	var sumX, sumY, sumXY, sumX2, sumY2 float64

	for i := 0; i < len(x); i++ {
		sumX += x[i]
		sumY += y[i]
		sumXY += x[i] * y[i]
		sumX2 += x[i] * x[i]
		sumY2 += y[i] * y[i]
	}

	numerator := n*sumXY - sumX*sumY
	denominator := math.Sqrt((n*sumX2 - sumX*sumX) * (n*sumY2 - sumY*sumY))

	if denominator == 0 {
		return 0
	}

	return numerator / denominator
}

// analyzeCorrelationWithPhysicalIDCLatency 分析与物理IDC延迟的关联
func analyzeCorrelationWithPhysicalIDCLatency(ctx *easy.Context, redis *easy.Redis, analysis *LongTailAnalysis, configMap map[string]*Config) {
	ctx.SLog.Info("=== Correlation with Physical IDC Latency Analysis ===").Set("logic_idc", analysis.LogicIDC).Print()

	// 获取物理IDC延迟统计数据
	physicalIDCLatencyData := getPhysicalIDCLatencyData(ctx, redis, analysis.LogicIDC)
	if len(physicalIDCLatencyData) == 0 {
		ctx.SLog.Info("no physical IDC latency data available for correlation analysis").Print()
		return
	}

	// 分析长尾分布与物理IDC延迟的关联
	for physicalIDC, idcStats := range analysis.PhysicalIDCDistrib {
		if latencyData, exists := physicalIDCLatencyData[physicalIDC]; exists {
			analyzeIDCCorrelation(ctx, physicalIDC, idcStats, latencyData)
		}
	}

	// 分析gossip组配置与延迟的关联
	analyzeGossipConfigCorrelation(ctx, analysis, physicalIDCLatencyData, configMap)
}

// getPhysicalIDCLatencyData 获取物理IDC延迟数据
func getPhysicalIDCLatencyData(ctx *easy.Context, redis *easy.Redis, logicIDC string) map[string]*PhysicalIDCLatencyStats {
	// 从Redis获取最新的物理IDC延迟统计数据
	latencyDataKey := fmt.Sprintf("physical-idc-latency-stats-%s", logicIDC)

	data, err := redis.Get(latencyDataKey)
	if err != nil {
		ctx.SLog.Warning("failed to get physical IDC latency data").Set("key", latencyDataKey).SetErr(err).Print()
		return nil
	}

	var latencyStats []*PhysicalIDCLatencyStats
	err = json.Unmarshal([]byte(data), &latencyStats)
	if err != nil {
		ctx.SLog.Warning("failed to unmarshal physical IDC latency data").SetErr(err).Print()
		return nil
	}

	// 转换为map格式，使用最新的total_latency数据
	result := make(map[string]*PhysicalIDCLatencyStats)
	for _, stats := range latencyStats {
		if stats.MetricType == "total_latency" {
			result[stats.PhysicalIDC] = stats
		}
	}

	return result
}

// analyzeIDCCorrelation 分析单个IDC的关联性
func analyzeIDCCorrelation(ctx *easy.Context, physicalIDC string, longTailStats *PhysicalIDCStats, latencyStats *PhysicalIDCLatencyStats) {
	ctx.SLog.Info("--- Physical IDC Correlation Analysis ---").Set("physical_idc", physicalIDC).Print()

	// 计算长尾比例
	longTailRatio := longTailStats.Percentage

	// 比较延迟指标
	ctx.SLog.Info("physical IDC metrics comparison").
		Set("physical_idc", physicalIDC).
		Set("long_tail_count", longTailStats.Count).
		Set("long_tail_ratio", fmt.Sprintf("%.2f%%", longTailRatio)).
		Set("long_tail_avg_latency", longTailStats.AvgLatency).
		Set("long_tail_max_latency", longTailStats.MaxLatency).
		Set("baseline_p95", latencyStats.P95).
		Set("baseline_p99", latencyStats.P99).
		Set("baseline_median", latencyStats.Median).
		Set("exceed_threshold_count", latencyStats.ExceedThresholdCount).
		Set("total_machine_count", latencyStats.TotalCount).
		Set("gossip_group_num", latencyStats.GossipGroupNum).Print()

	// 分析关联性
	baselineExceedRatio := float64(latencyStats.ExceedThresholdCount) / float64(latencyStats.TotalCount) * 100

	correlationLevel := "normal"
	if longTailRatio > baselineExceedRatio*2 {
		correlationLevel = "high_correlation"
	} else if longTailRatio > baselineExceedRatio*1.5 {
		correlationLevel = "moderate_correlation"
	}

	ctx.SLog.Info("correlation assessment").
		Set("physical_idc", physicalIDC).
		Set("long_tail_ratio", fmt.Sprintf("%.2f%%", longTailRatio)).
		Set("baseline_exceed_ratio", fmt.Sprintf("%.2f%%", baselineExceedRatio)).
		Set("correlation_level", correlationLevel).Print()

	// 如果存在高相关性，提供详细分析
	if correlationLevel == "high_correlation" {
		ctx.SLog.Warning("high correlation detected between long tail and baseline latency").
			Set("physical_idc", physicalIDC).
			Set("long_tail_avg_vs_p95", fmt.Sprintf("%.2f vs %.2f", longTailStats.AvgLatency, latencyStats.P95)).
			Set("long_tail_max_vs_p99", fmt.Sprintf("%.2f vs %.2f", longTailStats.MaxLatency, latencyStats.P99)).
			Set("gossip_groups_involved", len(longTailStats.GossipGroups)).Print()
	}
}

// analyzeGossipConfigCorrelation 分析gossip配置与延迟的关联
func analyzeGossipConfigCorrelation(ctx *easy.Context, analysis *LongTailAnalysis, physicalIDCLatencyData map[string]*PhysicalIDCLatencyStats, configMap map[string]*Config) {
	ctx.SLog.Info("--- Gossip Configuration Correlation Analysis ---").Set("logic_idc", analysis.LogicIDC).Print()

	config := analysis.CurrentConfig
	if config == nil {
		ctx.SLog.Info("no gossip config available for correlation analysis").Print()
		return
	}

	// 分析组数配置与性能的关联
	for physicalIDC, idcStats := range analysis.PhysicalIDCDistrib {
		groupNum := getGroupNumByIDC(*config, physicalIDC)

		if latencyData, exists := physicalIDCLatencyData[physicalIDC]; exists {
			avgMachinePerGroup := latencyData.AvgMachineNum

			// 计算组密度（机器数/组数）
			groupDensity := float64(latencyData.TotalCount) / float64(groupNum)

			ctx.SLog.Info("gossip config vs performance").
				Set("physical_idc", physicalIDC).
				Set("configured_group_num", groupNum).
				Set("actual_group_num", latencyData.GossipGroupNum).
				Set("total_machines", latencyData.TotalCount).
				Set("avg_machine_per_group", avgMachinePerGroup).
				Set("group_density", fmt.Sprintf("%.1f", groupDensity)).
				Set("long_tail_ratio", fmt.Sprintf("%.2f%%", idcStats.Percentage)).Print()

			// 分析组密度与长尾比例的关系
			if groupDensity > 200 { // 每组超过200台机器认为密度过高
				ctx.SLog.Warning("high group density detected").
					Set("physical_idc", physicalIDC).
					Set("group_density", fmt.Sprintf("%.1f", groupDensity)).
					Set("long_tail_ratio", fmt.Sprintf("%.2f%%", idcStats.Percentage)).
					Set("suggestion", "consider increasing group number").Print()
			} else if groupDensity < 50 { // 每组少于50台机器认为密度过低
				ctx.SLog.Info("low group density detected").
					Set("physical_idc", physicalIDC).
					Set("group_density", fmt.Sprintf("%.1f", groupDensity)).
					Set("long_tail_ratio", fmt.Sprintf("%.2f%%", idcStats.Percentage)).
					Set("suggestion", "consider decreasing group number").Print()
			}
		}
	}
}

// performCrossIDCAnalysis 执行跨IDC的对比分析
func performCrossIDCAnalysis(ctx *easy.Context, redis *easy.Redis, logicIDCs []string, tailCnt int, configMap map[string]*Config) {
	ctx.SLog.Info("=== Cross IDC Comparative Analysis ===").Print()

	// 收集所有IDC的分析结果
	idcAnalysisResults := make(map[string]*LongTailAnalysis)

	for _, logicIDC := range logicIDCs {
		key := fmt.Sprintf("p-6ufc-moniter-total-latency-%s-zset", logicIDC)
		slice, err := redis.ZRangeWithScores(key, -tailCnt, -1)
		if err != nil {
			ctx.SLog.Warning("get long tail data error for cross IDC analysis").Set("logic_idc", logicIDC).SetErr(err).Print()
			continue
		}

		if len(slice) == 0 {
			continue
		}

		gossipConfig, err := getGossipConfigByIDC(redis, logicIDC)
		if err != nil {
			ctx.SLog.Warning("get gossip config error for cross IDC analysis").Set("logic_idc", logicIDC).SetErr(err).Print()
			continue
		}

		analysis := analyzeLongTailCharacteristics(ctx, slice, logicIDC, gossipConfig)
		if analysis != nil {
			idcAnalysisResults[logicIDC] = analysis
		}
	}

	// 执行跨IDC对比
	compareCrossIDCPerformance(ctx, idcAnalysisResults)

	// 分析最佳实践
	identifyBestPractices(ctx, idcAnalysisResults, configMap)
}

// compareCrossIDCPerformance 对比跨IDC性能
func compareCrossIDCPerformance(ctx *easy.Context, idcAnalysisResults map[string]*LongTailAnalysis) {
	ctx.SLog.Info("--- Cross IDC Performance Comparison ---").Print()

	if len(idcAnalysisResults) < 2 {
		ctx.SLog.Info("insufficient IDCs for cross comparison").Print()
		return
	}

	// 计算每个IDC的关键指标
	idcMetrics := make(map[string]map[string]float64)

	for logicIDC, analysis := range idcAnalysisResults {
		metrics := make(map[string]float64)

		// 基本指标
		metrics["total_long_tail"] = float64(analysis.TotalLongTailCount)
		metrics["avg_latency"] = analysis.LatencyCharacteristics.AvgLatency
		metrics["p95_latency"] = analysis.LatencyCharacteristics.P95Latency
		metrics["p99_latency"] = analysis.LatencyCharacteristics.P99Latency

		// 计算物理IDC数量和平均长尾比例
		physicalIDCCount := len(analysis.PhysicalIDCDistrib)
		totalLongTailRatio := 0.0
		for _, stats := range analysis.PhysicalIDCDistrib {
			totalLongTailRatio += stats.Percentage
		}
		if physicalIDCCount > 0 {
			metrics["avg_long_tail_ratio"] = totalLongTailRatio / float64(physicalIDCCount)
		}
		metrics["physical_idc_count"] = float64(physicalIDCCount)

		// 计算gossip组数量和平均组大小
		gossipGroupCount := len(analysis.GossipGroupDistrib)
		totalGroupSize := 0
		for _, stats := range analysis.GossipGroupDistrib {
			totalGroupSize += stats.GroupSize
		}
		if gossipGroupCount > 0 {
			metrics["avg_group_size"] = float64(totalGroupSize) / float64(gossipGroupCount)
		}
		metrics["gossip_group_count"] = float64(gossipGroupCount)

		idcMetrics[logicIDC] = metrics

		ctx.SLog.Info("IDC performance metrics").
			Set("logic_idc", logicIDC).
			Set("total_long_tail", int(metrics["total_long_tail"])).
			Set("avg_latency", metrics["avg_latency"]).
			Set("p95_latency", metrics["p95_latency"]).
			Set("avg_long_tail_ratio", fmt.Sprintf("%.2f%%", metrics["avg_long_tail_ratio"])).
			Set("physical_idc_count", int(metrics["physical_idc_count"])).
			Set("gossip_group_count", int(metrics["gossip_group_count"])).
			Set("avg_group_size", fmt.Sprintf("%.1f", metrics["avg_group_size"])).Print()
	}

	// 找出最佳和最差的IDC
	var bestIDC, worstIDC string
	var bestRatio, worstRatio float64 = 100.0, 0.0

	for logicIDC, metrics := range idcMetrics {
		ratio := metrics["avg_long_tail_ratio"]
		if ratio < bestRatio {
			bestRatio = ratio
			bestIDC = logicIDC
		}
		if ratio > worstRatio {
			worstRatio = ratio
			worstIDC = logicIDC
		}
	}

	if bestIDC != "" && worstIDC != "" {
		ctx.SLog.Info("cross IDC performance ranking").
			Set("best_idc", bestIDC).
			Set("best_avg_long_tail_ratio", fmt.Sprintf("%.2f%%", bestRatio)).
			Set("worst_idc", worstIDC).
			Set("worst_avg_long_tail_ratio", fmt.Sprintf("%.2f%%", worstRatio)).
			Set("performance_gap", fmt.Sprintf("%.2f%%", worstRatio-bestRatio)).Print()
	}
}

// identifyBestPractices 识别最佳实践
func identifyBestPractices(ctx *easy.Context, idcAnalysisResults map[string]*LongTailAnalysis, configMap map[string]*Config) {
	ctx.SLog.Info("--- Best Practices Identification ---").Print()

	// 找出表现最好的IDC和配置
	var bestIDC string
	var bestAvgLongTailRatio float64 = 100.0

	for logicIDC, analysis := range idcAnalysisResults {
		totalRatio := 0.0
		count := 0
		for _, stats := range analysis.PhysicalIDCDistrib {
			totalRatio += stats.Percentage
			count++
		}
		if count > 0 {
			avgRatio := totalRatio / float64(count)
			if avgRatio < bestAvgLongTailRatio {
				bestAvgLongTailRatio = avgRatio
				bestIDC = logicIDC
			}
		}
	}

	if bestIDC != "" {
		bestAnalysis := idcAnalysisResults[bestIDC]
		bestConfig := bestAnalysis.CurrentConfig

		ctx.SLog.Info("best practice IDC identified").
			Set("best_idc", bestIDC).
			Set("avg_long_tail_ratio", fmt.Sprintf("%.2f%%", bestAvgLongTailRatio)).Print()

		if bestConfig != nil {
			ctx.SLog.Info("best practice configuration").
				Set("best_idc", bestIDC).
				Set("default_group_num", bestConfig.GroupNum).
				Set("rate", bestConfig.Rate).Print()

			// 输出各物理IDC的组数配置
			for physicalIDC, groupNum := range bestConfig.IDCGroupNum {
				if _, exists := bestAnalysis.PhysicalIDCDistrib[physicalIDC]; exists {
					stats := bestAnalysis.PhysicalIDCDistrib[physicalIDC]
					ctx.SLog.Info("best practice physical IDC config").
						Set("best_idc", bestIDC).
						Set("physical_idc", physicalIDC).
						Set("group_num", groupNum).
						Set("long_tail_ratio", fmt.Sprintf("%.2f%%", stats.Percentage)).Print()
				}
			}
		}

		// 提供改进建议
		provideBestPracticeRecommendations(ctx, bestIDC, bestAnalysis, idcAnalysisResults)
	}
}

// provideBestPracticeRecommendations 提供基于最佳实践的改进建议
func provideBestPracticeRecommendations(ctx *easy.Context, bestIDC string, bestAnalysis *LongTailAnalysis, allResults map[string]*LongTailAnalysis) {
	ctx.SLog.Info("--- Best Practice Recommendations ---").Print()

	bestConfig := bestAnalysis.CurrentConfig
	if bestConfig == nil {
		return
	}

	for logicIDC, analysis := range allResults {
		if logicIDC == bestIDC {
			continue
		}

		ctx.SLog.Info("recommendations for IDC").Set("target_idc", logicIDC).Set("reference_idc", bestIDC).Print()

		currentConfig := analysis.CurrentConfig
		if currentConfig == nil {
			continue
		}

		// 比较配置差异并提供建议
		if currentConfig.GroupNum != bestConfig.GroupNum {
			ctx.SLog.Info("group number recommendation").
				Set("target_idc", logicIDC).
				Set("current_group_num", currentConfig.GroupNum).
				Set("recommended_group_num", bestConfig.GroupNum).
				Set("reference_idc", bestIDC).Print()
		}

		if math.Abs(currentConfig.Rate-bestConfig.Rate) > 0.01 {
			ctx.SLog.Info("rate recommendation").
				Set("target_idc", logicIDC).
				Set("current_rate", currentConfig.Rate).
				Set("recommended_rate", bestConfig.Rate).
				Set("reference_idc", bestIDC).Print()
		}

		// 比较物理IDC配置
		for physicalIDC, bestGroupNum := range bestConfig.IDCGroupNum {
			if currentGroupNum, exists := currentConfig.IDCGroupNum[physicalIDC]; exists {
				if currentGroupNum != bestGroupNum {
					ctx.SLog.Info("physical IDC group number recommendation").
						Set("target_idc", logicIDC).
						Set("physical_idc", physicalIDC).
						Set("current_group_num", currentGroupNum).
						Set("recommended_group_num", bestGroupNum).
						Set("reference_idc", bestIDC).Print()
				}
			}
		}
	}
}

// calculateLatencyCharacteristics 计算延迟特征
func calculateLatencyCharacteristics(latencies []float64) *LatencyCharacteristics {
	if len(latencies) == 0 {
		return &LatencyCharacteristics{}
	}

	// 排序
	sortedLatencies := make([]float64, len(latencies))
	copy(sortedLatencies, latencies)
	sort.Float64s(sortedLatencies)

	// 计算基本统计量
	min := sortedLatencies[0]
	max := sortedLatencies[len(sortedLatencies)-1]

	// 计算平均值
	sum := 0.0
	for _, latency := range latencies {
		sum += latency
	}
	avg := sum / float64(len(latencies))

	// 计算中位数
	median := calculatePercentile(sortedLatencies, 0.5)

	// 计算P95和P99
	p95 := calculatePercentile(sortedLatencies, 0.95)
	p99 := calculatePercentile(sortedLatencies, 0.99)

	// 计算标准差
	variance := 0.0
	for _, latency := range latencies {
		variance += math.Pow(latency-avg, 2)
	}
	stdDev := math.Sqrt(variance / float64(len(latencies)))

	return &LatencyCharacteristics{
		MinLatency:    min,
		MaxLatency:    max,
		AvgLatency:    avg,
		MedianLatency: median,
		P95Latency:    p95,
		P99Latency:    p99,
		StdDeviation:  stdDev,
	}
}

// analyzeByPhysicalIDC 按物理IDC分析长尾数据
func analyzeByPhysicalIDC(ctx *easy.Context, analysis *LongTailAnalysis, ipLatencyMap map[string]float64) {
	physicalIDCMap := make(map[string][]string)        // 物理IDC -> IP列表
	physicalIDCLatencies := make(map[string][]float64) // 物理IDC -> 延迟列表

	// 按物理IDC分组
	for ip, latency := range ipLatencyMap {
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		physicalIDCMap[physicalIDC] = append(physicalIDCMap[physicalIDC], ip)
		physicalIDCLatencies[physicalIDC] = append(physicalIDCLatencies[physicalIDC], latency)
	}

	// 计算每个物理IDC的统计信息
	for physicalIDC, ips := range physicalIDCMap {
		latencies := physicalIDCLatencies[physicalIDC]
		if len(latencies) == 0 {
			continue
		}

		// 计算统计量
		sum := 0.0
		max := latencies[0]
		for _, latency := range latencies {
			sum += latency
			if latency > max {
				max = latency
			}
		}
		avg := sum / float64(len(latencies))

		// 获取该物理IDC涉及的gossip组
		gossipGroups := getGossipGroupsForPhysicalIDC(ctx, physicalIDC, analysis.CurrentConfig)

		stats := &PhysicalIDCStats{
			PhysicalIDC:  physicalIDC,
			Count:        len(ips),
			Percentage:   float64(len(ips)) / float64(analysis.TotalLongTailCount) * 100,
			AvgLatency:   avg,
			MaxLatency:   max,
			IPs:          ips,
			GossipGroups: gossipGroups,
		}

		analysis.PhysicalIDCDistrib[physicalIDC] = stats
	}
}

// getGossipGroupsForPhysicalIDC 获取物理IDC涉及的gossip组
func getGossipGroupsForPhysicalIDC(ctx *easy.Context, physicalIDC string, config *Config) []int {
	groupNum := getGroupNumByIDC(*config, physicalIDC)
	groups := make([]int, groupNum)
	for i := 0; i < groupNum; i++ {
		groups[i] = i
	}
	return groups
}

// analyzeByGossipGroup 按gossip组分析长尾数据
func analyzeByGossipGroup(ctx *easy.Context, analysis *LongTailAnalysis, ipLatencyMap map[string]float64, config *Config) {
	redis := easy.NewRedis(ctx, "redis")

	// 获取逻辑IDC
	logicalIDC := analysis.LogicIDC

	// 遍历所有gossip组
	for physicalIDC := range analysis.PhysicalIDCDistrib {
		groupNum := getGroupNumByIDC(*config, physicalIDC)

		for groupIndex := 0; groupIndex < groupNum; groupIndex++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", logicalIDC, physicalIDC, groupIndex)

			// 获取该组的所有IP
			groupIPs, err := redis.SMembers(key)
			if err != nil {
				ctx.SLog.Warning("get gossip group members error").Set("key", key).SetErr(err).Print()
				continue
			}

			// 统计该组中的长尾IP
			var groupLongTailIPs []string
			var groupLatencies []float64

			for _, ip := range groupIPs {
				if latency, exists := ipLatencyMap[ip]; exists {
					groupLongTailIPs = append(groupLongTailIPs, ip)
					groupLatencies = append(groupLatencies, latency)
				}
			}

			if len(groupLongTailIPs) == 0 {
				continue
			}

			// 计算统计量
			sum := 0.0
			max := groupLatencies[0]
			for _, latency := range groupLatencies {
				sum += latency
				if latency > max {
					max = latency
				}
			}
			avg := sum / float64(len(groupLatencies))

			groupKey := fmt.Sprintf("%s-%d", physicalIDC, groupIndex)
			stats := &GossipGroupStats{
				GroupIndex:  groupIndex,
				PhysicalIDC: physicalIDC,
				Count:       len(groupLongTailIPs),
				Percentage:  float64(len(groupLongTailIPs)) / float64(analysis.TotalLongTailCount) * 100,
				AvgLatency:  avg,
				MaxLatency:  max,
				IPs:         groupLongTailIPs,
				GroupSize:   len(groupIPs),
			}

			analysis.GossipGroupDistrib[groupKey] = stats
		}
	}
}

// outputLongTailAnalysis 输出长尾分析结果
func outputLongTailAnalysis(ctx *easy.Context, analysis *LongTailAnalysis) {
	if analysis == nil {
		return
	}

	ctx.SLog.Info("=== Long Tail Analysis Results ===").
		Set("logic_idc", analysis.LogicIDC).
		Set("total_long_tail_count", analysis.TotalLongTailCount).Print()

	// 输出延迟特征
	if analysis.LatencyCharacteristics != nil {
		ctx.SLog.Info("latency characteristics").
			Set("logic_idc", analysis.LogicIDC).
			Set("min_latency", analysis.LatencyCharacteristics.MinLatency).
			Set("max_latency", analysis.LatencyCharacteristics.MaxLatency).
			Set("avg_latency", analysis.LatencyCharacteristics.AvgLatency).
			Set("median_latency", analysis.LatencyCharacteristics.MedianLatency).
			Set("p95_latency", analysis.LatencyCharacteristics.P95Latency).
			Set("p99_latency", analysis.LatencyCharacteristics.P99Latency).
			Set("std_deviation", analysis.LatencyCharacteristics.StdDeviation).Print()
	}

	// 输出物理IDC分布
	ctx.SLog.Info("=== Physical IDC Distribution ===").Set("logic_idc", analysis.LogicIDC).Print()
	for physicalIDC, stats := range analysis.PhysicalIDCDistrib {
		ctx.SLog.Info("physical IDC long tail stats").
			Set("logic_idc", analysis.LogicIDC).
			Set("physical_idc", physicalIDC).
			Set("count", stats.Count).
			Set("percentage", fmt.Sprintf("%.2f%%", stats.Percentage)).
			Set("avg_latency", stats.AvgLatency).
			Set("max_latency", stats.MaxLatency).
			Set("gossip_groups", stats.GossipGroups).
			Set("ip_count", len(stats.IPs)).Print()
	}

	// 输出gossip组分布
	ctx.SLog.Info("=== Gossip Group Distribution ===").Set("logic_idc", analysis.LogicIDC).Print()
	for groupKey, stats := range analysis.GossipGroupDistrib {
		ctx.SLog.Info("gossip group long tail stats").
			Set("logic_idc", analysis.LogicIDC).
			Set("group_key", groupKey).
			Set("physical_idc", stats.PhysicalIDC).
			Set("group_index", stats.GroupIndex).
			Set("count", stats.Count).
			Set("percentage", fmt.Sprintf("%.2f%%", stats.Percentage)).
			Set("avg_latency", stats.AvgLatency).
			Set("max_latency", stats.MaxLatency).
			Set("group_size", stats.GroupSize).
			Set("long_tail_ratio", fmt.Sprintf("%.2f%%", float64(stats.Count)/float64(stats.GroupSize)*100)).Print()
	}
}

// generateOptimizationSuggestions 生成优化建议
func generateOptimizationSuggestions(ctx *easy.Context, analysis *LongTailAnalysis) *OptimizationSuggestion {
	if analysis == nil || analysis.CurrentConfig == nil {
		return nil
	}

	suggestion := &OptimizationSuggestion{
		LogicIDC:           analysis.LogicIDC,
		CurrentGroupNum:    analysis.CurrentConfig.GroupNum,
		PhysicalIDCChanges: make(map[string]int),
	}

	// 分析长尾分布特征
	highConcentrationThreshold := 0.3 // 如果某个物理IDC占长尾的30%以上，认为是高集中度

	var problematicIDCs []string
	var reasons []string

	// 检查物理IDC分布
	for physicalIDC, stats := range analysis.PhysicalIDCDistrib {
		concentration := stats.Percentage / 100.0

		if concentration > highConcentrationThreshold {
			problematicIDCs = append(problematicIDCs, physicalIDC)
			reasons = append(reasons, fmt.Sprintf("%s占长尾%.1f%%，集中度过高", physicalIDC, stats.Percentage))
		}
	}

	// 检查gossip组内长尾比例
	for groupKey, stats := range analysis.GossipGroupDistrib {
		if stats.GroupSize > 0 {
			longTailRatio := float64(stats.Count) / float64(stats.GroupSize)
			if longTailRatio > 0.2 { // 如果组内20%以上都是长尾，说明该组有问题
				reasons = append(reasons, fmt.Sprintf("组%s长尾比例%.1f%%过高", groupKey, longTailRatio*100))
			}
		}
	}

	// 生成建议
	if len(problematicIDCs) > 0 {
		// 建议增加问题物理IDC的组数
		for _, physicalIDC := range problematicIDCs {
			currentGroupNum := getGroupNumByIDC(*analysis.CurrentConfig, physicalIDC)
			suggestedIncrease := int(math.Ceil(float64(currentGroupNum) * 0.5)) // 增加50%
			suggestion.PhysicalIDCChanges[physicalIDC] = currentGroupNum + suggestedIncrease
		}

		suggestion.SuggestedGroupNum = analysis.CurrentConfig.GroupNum + 5 // 整体增加5个组
		suggestion.Reason = strings.Join(reasons, "; ")
		suggestion.ExpectedImprovement = "通过增加gossip组数，减少每组机器数量，降低长尾延迟集中度"
		suggestion.RiskAssessment = "低风险：增加组数会增加内存使用，但能有效分散负载"
	} else {
		// 没有明显问题
		suggestion.SuggestedGroupNum = analysis.CurrentConfig.GroupNum
		suggestion.Reason = "当前配置合理，长尾分布相对均匀"
		suggestion.ExpectedImprovement = "保持当前配置"
		suggestion.RiskAssessment = "无风险"
	}

	return suggestion
}

// outputOptimizationSuggestions 输出优化建议
func outputOptimizationSuggestions(ctx *easy.Context, suggestion *OptimizationSuggestion) {
	if suggestion == nil {
		return
	}

	ctx.SLog.Info("=== Optimization Suggestions ===").
		Set("logic_idc", suggestion.LogicIDC).
		Set("current_group_num", suggestion.CurrentGroupNum).
		Set("suggested_group_num", suggestion.SuggestedGroupNum).
		Set("reason", suggestion.Reason).
		Set("expected_improvement", suggestion.ExpectedImprovement).
		Set("risk_assessment", suggestion.RiskAssessment).Print()

	// 输出各物理IDC的建议变化
	if len(suggestion.PhysicalIDCChanges) > 0 {
		ctx.SLog.Info("=== Physical IDC Group Changes ===").Set("logic_idc", suggestion.LogicIDC).Print()
		for physicalIDC, suggestedGroupNum := range suggestion.PhysicalIDCChanges {
			ctx.SLog.Info("physical IDC group change suggestion").
				Set("logic_idc", suggestion.LogicIDC).
				Set("physical_idc", physicalIDC).
				Set("suggested_group_num", suggestedGroupNum).Print()
		}
	}
}
