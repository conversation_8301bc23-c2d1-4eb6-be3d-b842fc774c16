package gossiputil

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

var idc = []string{
	"bjhw",
	"gajl",
	"yq01",
	"bdjl",
	"xafj",
	"xakd",
	"yq02",
	"bddwd",
	"bddx",
	"bjyz",
	"xaky",
}

var idcCounts = map[string]int{
	"bjhw":  740,
	"gajl":  125,
	"yq01":  8759,
	"bdjl":  3746,
	"xafj":  5340,
	"xakd":  3114,
	"yq02":  3638,
	"bddwd": 2111,
	"bddx":  1983,
	"bjyz":  971,
	"xaky":  4317,
}

type Config struct {
	Enable      bool           `json:"enable"`
	IDCGroupNum map[string]int `json:"idc_group_num"`
	Rate        float64        `json:"rate"`
	GroupNum    int            `json:"group_num"`
}

// PhysicalIDCLatencyStats 物理IDC延迟统计结构
type PhysicalIDCLatencyStats struct {
	PhysicalIDC          string  `json:"physical_idc"`           // 物理机房名称，如 yq01, yq02, xaky 等
	MetricType           string  `json:"metric_type"`            // 指标类型：worker_latency, privileged_latency, agent_latency, total_latency
	P100                 float64 `json:"p100"`                   // P100 百分位（最大值）
	P99                  float64 `json:"p99"`                    // P99 百分位
	P95                  float64 `json:"p95"`                    // P95 百分位
	P90                  float64 `json:"p90"`                    // P90 百分位
	P80                  float64 `json:"p80"`                    // P80 百分位
	Median               float64 `json:"median"`                 // 中位数
	TrimmedMean          float64 `json:"trimmed_mean"`           // 去除最大最小各5个机器的平均数
	TotalCount           int     `json:"total_count"`            // 总机器数
	ExceedThresholdCount int     `json:"exceed_threshold_count"` // 超过阈值的机器数
	ThresholdValue       int64   `json:"threshold_value"`        // 阈值
	GossipGroupNum       int     `json:"gossip_group_num"`       // 该物理IDC的gossip组数
	AvgMachineNum        int     `json:"avg_machine_num"`        // 平均每组机器数
	Timestamp            int64   `json:"timestamp"`              // 统计时间戳
}

// LongTailAnalysis 长尾分析结构
type LongTailAnalysis struct {
	LogicIDC               string                       `json:"logic_idc"`               // 逻辑机房名称
	TotalLongTailCount     int                          `json:"total_long_tail_count"`   // 长尾总数
	PhysicalIDCDistrib     map[string]*PhysicalIDCStats `json:"physical_idc_distrib"`    // 按物理IDC分布
	GossipGroupDistrib     map[string]*GossipGroupStats `json:"gossip_group_distrib"`    // 按gossip组分布
	LatencyCharacteristics *LatencyCharacteristics      `json:"latency_characteristics"` // 延迟特征
	CurrentConfig          *Config                      `json:"current_config"`          // 当前配置
}

// PhysicalIDCStats 物理IDC统计
type PhysicalIDCStats struct {
	PhysicalIDC  string   `json:"physical_idc"`  // 物理IDC名称
	Count        int      `json:"count"`         // 长尾数量
	Percentage   float64  `json:"percentage"`    // 占比
	AvgLatency   float64  `json:"avg_latency"`   // 平均延迟
	MaxLatency   float64  `json:"max_latency"`   // 最大延迟
	IPs          []string `json:"ips"`           // 涉及的IP列表
	GossipGroups []int    `json:"gossip_groups"` // 涉及的gossip组
}

// GossipGroupStats gossip组统计
type GossipGroupStats struct {
	GroupIndex  int      `json:"group_index"`  // 组索引
	PhysicalIDC string   `json:"physical_idc"` // 物理IDC
	Count       int      `json:"count"`        // 长尾数量
	Percentage  float64  `json:"percentage"`   // 占比
	AvgLatency  float64  `json:"avg_latency"`  // 平均延迟
	MaxLatency  float64  `json:"max_latency"`  // 最大延迟
	IPs         []string `json:"ips"`          // 涉及的IP列表
	GroupSize   int      `json:"group_size"`   // 组大小
}

// LatencyCharacteristics 延迟特征
type LatencyCharacteristics struct {
	MinLatency    float64 `json:"min_latency"`    // 最小延迟
	MaxLatency    float64 `json:"max_latency"`    // 最大延迟
	AvgLatency    float64 `json:"avg_latency"`    // 平均延迟
	MedianLatency float64 `json:"median_latency"` // 中位数延迟
	P95Latency    float64 `json:"p95_latency"`    // P95延迟
	P99Latency    float64 `json:"p99_latency"`    // P99延迟
	StdDeviation  float64 `json:"std_deviation"`  // 标准差
}

// OptimizationSuggestion 优化建议
type OptimizationSuggestion struct {
	LogicIDC            string         `json:"logic_idc"`            // 逻辑机房
	CurrentGroupNum     int            `json:"current_group_num"`    // 当前组数
	SuggestedGroupNum   int            `json:"suggested_group_num"`  // 建议组数
	Reason              string         `json:"reason"`               // 建议原因
	ExpectedImprovement string         `json:"expected_improvement"` // 预期改善
	PhysicalIDCChanges  map[string]int `json:"physical_idc_changes"` // 各物理IDC的组数变化
	RiskAssessment      string         `json:"risk_assessment"`      // 风险评估
}

var defaultConfig = Config{
	Enable: true,
	IDCGroupNum: map[string]int{
		"default": 20,
		"gzhxy":   1,
		"bddwd":   10,
		"bddx":    7,
		"bjyz":    9,
		"st01":    3,
		"bjhw":    7,
	},
	Rate:     0.1,
	GroupNum: 20,
}

// Analaysis 函数用于执行分析操作
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func Analaysis(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")

	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("analysis err").SetErr(err).Print()
		return err
	}

	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 对于每个物理 idc 内，需要监控其 gossip 分组情况
	for phyIdc, _ := range instanceInfo.InstanceInfo {
		// 分析每个 idc 内部分组情况
		ufclogidcIdc := netutil.GetIDCByHostname(phyIdc)
		gossipConfig, ok := configMap[ufclogidcIdc]
		if !ok {
			continue
		}
		groupNum := getGroupNumByIDC(*gossipConfig, phyIdc)
		for i := 0; i < groupNum; i++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", ufclogidcIdc, phyIdc, i)
			scardCnt, err := redis.Scard(key)
			if err != nil {
				ctx.SLog.Warning("get scard error").SetErr(err).Print()
				return err
			}

			ctx.SLog.Info("idc gossip group info").
				Set("idc", phyIdc).
				Set("idc_instance_cnt", instanceInfo.InstanceCnt[phyIdc]).
				Set("redis_key", key).
				Set("group_num", groupNum).
				Set("group_index", i).
				Set("scard_cnt", scardCnt).
				Print()
		}

	}

	return nil
}

func getGroupNumByIDC(gossipConfig Config, idc string) int {
	num, ok := gossipConfig.IDCGroupNum[idc]
	if !ok {
		num = gossipConfig.GroupNum
	}
	return num

}

func getGossipConfigMap(redis *easy.Redis) (map[string]*Config, error) {
	idcs := []string{
		"beijing",
		"yangquan",
		"xian",
	}

	configMap := make(map[string]*Config)
	for _, idc := range idcs {
		config, err := getGossipConfigByIDC(redis, idc)
		if err != nil {
			return nil, err
		}
		configMap[idc] = config
	}
	return configMap, nil
}
func getGossipConfigByIDC(redis *easy.Redis, idc string) (*Config, error) {
	key := fmt.Sprintf("p-3ufc-%s-config-gossip", idc)
	value, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var config Config

	err = json.Unmarshal([]byte(value), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}
	return &config, nil
}

// AnalysisPhysicalIDCLatency 分析每个物理IDC的延迟情况
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func AnalysisPhysicalIDCLatency(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")
	timestamp := time.Now().Unix()

	ctx.SLog.Info("Starting physical IDC latency analysis").Print()

	// 获取gossip配置信息
	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("get gossip config map error").SetErr(err).Print()
		return err
	}

	// 获取实例信息
	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 统计各种延迟类型
	metricTypes := []struct {
		name      string
		zsetKey   string
		threshold int64
	}{
		{"worker_latency", cacheutil.GetUFCWorkerLatencyZSetName(), 60},
		{"privileged_latency", cacheutil.GetUFCPrivilegedLatencyZSetName(), 60},
		{"total_latency", cacheutil.GetUFCTotalLatencyZSetName(), 120},
	}

	for _, metric := range metricTypes {
		ctx.SLog.Info("Processing metric type").Set("metric_type", metric.name).Print()

		stats, err := calculatePhysicalIDCLatencyStats(ctx, redis, metric.name, metric.zsetKey, metric.threshold, configMap, instanceInfo, timestamp)
		if err != nil {
			ctx.SLog.Warning("calculate physical IDC latency stats error").Set("metric_type", metric.name).SetErr(err).Print()
			continue
		}

		// 输出统计结果
		for _, stat := range stats {
			ctx.SLog.Info("physical IDC latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("avg_machine_num", stat.AvgMachineNum).
				Set("median", stat.Median).
				Set("trimmed_mean", stat.TrimmedMean).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	// 处理 Agent 延迟统计
	ctx.SLog.Info("Processing agent latency").Print()
	agentStats, err := calculatePhysicalIDCAgentLatencyStats(ctx, redis, cacheutil.GetUFCIPMtimesZSetName(), 300, configMap, instanceInfo, timestamp)
	if err != nil {
		ctx.SLog.Warning("calculate physical IDC agent latency stats error").SetErr(err).Print()
	} else {
		for _, stat := range agentStats {
			ctx.SLog.Info("physical IDC agent latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	ctx.SLog.Info("Physical IDC latency analysis completed").Print()
	return nil
}

// calculatePhysicalIDCLatencyStats 计算物理IDC延迟统计
func calculatePhysicalIDCLatencyStats(ctx *easy.Context, redis *easy.Redis, metricType, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, timestamp int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		latency := conv.ToFloat64(slice[2*i+1])

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], latency)
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           metricType,
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,

			Timestamp: timestamp,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePhysicalIDCAgentLatencyStats 计算物理IDC Agent延迟统计（mtime 到当前时间的差值）
func calculatePhysicalIDCAgentLatencyStats(ctx *easy.Context, redis *easy.Redis, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, currentTime int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		mtime := conv.ToInt64(slice[2*i+1])

		if mtime <= 0 {
			// 跳过无效的 mtime
			continue
		}

		latency := currentTime - mtime
		if latency < 0 {
			latency = 0
		}

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], float64(latency))
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           "agent_latency",
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,
			Timestamp:            currentTime,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePercentile 计算百分位数
func calculatePercentile(sortedData []float64, percentile float64) float64 {
	if len(sortedData) == 0 {
		return 0
	}

	if len(sortedData) == 1 {
		return sortedData[0]
	}

	// 计算索引位置
	index := percentile * float64(len(sortedData)-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if upper >= len(sortedData) {
		upper = len(sortedData) - 1
	}
	if lower < 0 {
		lower = 0
	}

	if lower == upper {
		return sortedData[lower]
	}

	// 线性插值
	weight := index - float64(lower)
	return sortedData[lower]*(1-weight) + sortedData[upper]*weight
}

// 分析每个机房长尾数据，
func AnalysisLongTailResult(ctx *easy.Context, tailCnt int) error {
	// zrange p-6ufc-moniter-total-latency-yangquan-zset -100 -1 WITHSCORES [WITH
	logicIdcs := []string{"beijing", "yangquan", "xian"}
	redis := easy.NewRedis(ctx, "redis")
	for _, logicIdc := range logicIdcs {
		analysisLongTailResultByIDC(ctx, redis, logicIdc, tailCnt)
	}

	return nil
}

func analysisLongTailResultByIDC(ctx *easy.Context, redis *easy.Redis, logicIDC string, tailCnt int) {
	key := fmt.Sprintf("p-6ufc-moniter-total-latency-%s-zset", logicIDC)
	slice, err := redis.ZRangeWithScores(key, -tailCnt, -1)
	if err != nil {
		ctx.SLog.Warning("get long tail data error").Set("key", key).SetErr(err).Print()
		return
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no long tail data").Set("key", key).Print()
		return
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("invalid long tail data length").Set("key", key).Set("len", len(slice)).Print()
		return
	}

	ctx.SLog.Info("analyzing long tail data").Set("logic_idc", logicIDC).Set("tail_count", tailCnt).Print()

	// 获取gossip配置
	gossipConfig, err := getGossipConfigByIDC(redis, logicIDC)
	if err != nil {
		ctx.SLog.Warning("get gossip config error").Set("logic_idc", logicIDC).SetErr(err).Print()
		return
	}

	// 分析长尾数据的特征
	longTailAnalysis := analyzeLongTailCharacteristics(ctx, slice, logicIDC, gossipConfig)

	// 输出分析结果
	outputLongTailAnalysis(ctx, longTailAnalysis)

	// 提供优化建议
	optimizationSuggestions := generateOptimizationSuggestions(ctx, longTailAnalysis)
	outputOptimizationSuggestions(ctx, optimizationSuggestions)
}

// getGossipGroupNumByPhysicalIDC 获取物理IDC的gossip组数
func getGossipGroupNumByPhysicalIDC(physicalIDC string, configMap map[string]*Config, _ *common.InstanceInfo) int {
	// 首先通过物理IDC获取逻辑IDC
	logicalIDC := netutil.GetIDCByHostname(physicalIDC + ".baidu.com")

	// 获取该逻辑IDC的gossip配置
	gossipConfig, ok := configMap[logicalIDC]
	if !ok {
		// 如果没有配置，返回默认值
		return defaultConfig.GroupNum
	}

	// 获取该物理IDC的组数配置
	groupNum := getGroupNumByIDC(*gossipConfig, physicalIDC)
	return groupNum
}

// analyzeLongTailCharacteristics 分析长尾数据特征
func analyzeLongTailCharacteristics(ctx *easy.Context, slice []string, logicIDC string, gossipConfig *Config) *LongTailAnalysis {
	totalCount := len(slice) / 2
	if totalCount == 0 {
		return nil
	}

	analysis := &LongTailAnalysis{
		LogicIDC:           logicIDC,
		TotalLongTailCount: totalCount,
		PhysicalIDCDistrib: make(map[string]*PhysicalIDCStats),
		GossipGroupDistrib: make(map[string]*GossipGroupStats),
		CurrentConfig:      gossipConfig,
	}

	// 收集所有延迟数据和IP信息
	var latencies []float64
	ipLatencyMap := make(map[string]float64)

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		latency := conv.ToFloat64(slice[2*i+1])

		latencies = append(latencies, latency)
		ipLatencyMap[ip] = latency
	}

	// 计算延迟特征
	analysis.LatencyCharacteristics = calculateLatencyCharacteristics(latencies)

	// 按物理IDC分组分析
	analyzeByPhysicalIDC(ctx, analysis, ipLatencyMap)

	// 按gossip组分析
	analyzeByGossipGroup(ctx, analysis, ipLatencyMap, gossipConfig)

	return analysis
}

// calculateLatencyCharacteristics 计算延迟特征
func calculateLatencyCharacteristics(latencies []float64) *LatencyCharacteristics {
	if len(latencies) == 0 {
		return &LatencyCharacteristics{}
	}

	// 排序
	sortedLatencies := make([]float64, len(latencies))
	copy(sortedLatencies, latencies)
	sort.Float64s(sortedLatencies)

	// 计算基本统计量
	min := sortedLatencies[0]
	max := sortedLatencies[len(sortedLatencies)-1]

	// 计算平均值
	sum := 0.0
	for _, latency := range latencies {
		sum += latency
	}
	avg := sum / float64(len(latencies))

	// 计算中位数
	median := calculatePercentile(sortedLatencies, 0.5)

	// 计算P95和P99
	p95 := calculatePercentile(sortedLatencies, 0.95)
	p99 := calculatePercentile(sortedLatencies, 0.99)

	// 计算标准差
	variance := 0.0
	for _, latency := range latencies {
		variance += math.Pow(latency-avg, 2)
	}
	stdDev := math.Sqrt(variance / float64(len(latencies)))

	return &LatencyCharacteristics{
		MinLatency:    min,
		MaxLatency:    max,
		AvgLatency:    avg,
		MedianLatency: median,
		P95Latency:    p95,
		P99Latency:    p99,
		StdDeviation:  stdDev,
	}
}

// analyzeByPhysicalIDC 按物理IDC分析长尾数据
func analyzeByPhysicalIDC(ctx *easy.Context, analysis *LongTailAnalysis, ipLatencyMap map[string]float64) {
	physicalIDCMap := make(map[string][]string)        // 物理IDC -> IP列表
	physicalIDCLatencies := make(map[string][]float64) // 物理IDC -> 延迟列表

	// 按物理IDC分组
	for ip, latency := range ipLatencyMap {
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		physicalIDCMap[physicalIDC] = append(physicalIDCMap[physicalIDC], ip)
		physicalIDCLatencies[physicalIDC] = append(physicalIDCLatencies[physicalIDC], latency)
	}

	// 计算每个物理IDC的统计信息
	for physicalIDC, ips := range physicalIDCMap {
		latencies := physicalIDCLatencies[physicalIDC]
		if len(latencies) == 0 {
			continue
		}

		// 计算统计量
		sum := 0.0
		max := latencies[0]
		for _, latency := range latencies {
			sum += latency
			if latency > max {
				max = latency
			}
		}
		avg := sum / float64(len(latencies))

		// 获取该物理IDC涉及的gossip组
		gossipGroups := getGossipGroupsForPhysicalIDC(ctx, physicalIDC, analysis.CurrentConfig)

		stats := &PhysicalIDCStats{
			PhysicalIDC:  physicalIDC,
			Count:        len(ips),
			Percentage:   float64(len(ips)) / float64(analysis.TotalLongTailCount) * 100,
			AvgLatency:   avg,
			MaxLatency:   max,
			IPs:          ips,
			GossipGroups: gossipGroups,
		}

		analysis.PhysicalIDCDistrib[physicalIDC] = stats
	}
}

// getGossipGroupsForPhysicalIDC 获取物理IDC涉及的gossip组
func getGossipGroupsForPhysicalIDC(ctx *easy.Context, physicalIDC string, config *Config) []int {
	groupNum := getGroupNumByIDC(*config, physicalIDC)
	groups := make([]int, groupNum)
	for i := 0; i < groupNum; i++ {
		groups[i] = i
	}
	return groups
}

// analyzeByGossipGroup 按gossip组分析长尾数据
func analyzeByGossipGroup(ctx *easy.Context, analysis *LongTailAnalysis, ipLatencyMap map[string]float64, config *Config) {
	redis := easy.NewRedis(ctx, "redis")

	// 获取逻辑IDC
	logicalIDC := analysis.LogicIDC

	// 遍历所有gossip组
	for physicalIDC := range analysis.PhysicalIDCDistrib {
		groupNum := getGroupNumByIDC(*config, physicalIDC)

		for groupIndex := 0; groupIndex < groupNum; groupIndex++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", logicalIDC, physicalIDC, groupIndex)

			// 获取该组的所有IP
			groupIPs, err := redis.SMembers(key)
			if err != nil {
				ctx.SLog.Warning("get gossip group members error").Set("key", key).SetErr(err).Print()
				continue
			}

			// 统计该组中的长尾IP
			var groupLongTailIPs []string
			var groupLatencies []float64

			for _, ip := range groupIPs {
				if latency, exists := ipLatencyMap[ip]; exists {
					groupLongTailIPs = append(groupLongTailIPs, ip)
					groupLatencies = append(groupLatencies, latency)
				}
			}

			if len(groupLongTailIPs) == 0 {
				continue
			}

			// 计算统计量
			sum := 0.0
			max := groupLatencies[0]
			for _, latency := range groupLatencies {
				sum += latency
				if latency > max {
					max = latency
				}
			}
			avg := sum / float64(len(groupLatencies))

			groupKey := fmt.Sprintf("%s-%d", physicalIDC, groupIndex)
			stats := &GossipGroupStats{
				GroupIndex:  groupIndex,
				PhysicalIDC: physicalIDC,
				Count:       len(groupLongTailIPs),
				Percentage:  float64(len(groupLongTailIPs)) / float64(analysis.TotalLongTailCount) * 100,
				AvgLatency:  avg,
				MaxLatency:  max,
				IPs:         groupLongTailIPs,
				GroupSize:   len(groupIPs),
			}

			analysis.GossipGroupDistrib[groupKey] = stats
		}
	}
}

// outputLongTailAnalysis 输出长尾分析结果
func outputLongTailAnalysis(ctx *easy.Context, analysis *LongTailAnalysis) {
	if analysis == nil {
		return
	}

	ctx.SLog.Info("=== Long Tail Analysis Results ===").
		Set("logic_idc", analysis.LogicIDC).
		Set("total_long_tail_count", analysis.TotalLongTailCount).Print()

	// 输出延迟特征
	if analysis.LatencyCharacteristics != nil {
		ctx.SLog.Info("latency characteristics").
			Set("logic_idc", analysis.LogicIDC).
			Set("min_latency", analysis.LatencyCharacteristics.MinLatency).
			Set("max_latency", analysis.LatencyCharacteristics.MaxLatency).
			Set("avg_latency", analysis.LatencyCharacteristics.AvgLatency).
			Set("median_latency", analysis.LatencyCharacteristics.MedianLatency).
			Set("p95_latency", analysis.LatencyCharacteristics.P95Latency).
			Set("p99_latency", analysis.LatencyCharacteristics.P99Latency).
			Set("std_deviation", analysis.LatencyCharacteristics.StdDeviation).Print()
	}

	// 输出物理IDC分布
	ctx.SLog.Info("=== Physical IDC Distribution ===").Set("logic_idc", analysis.LogicIDC).Print()
	for physicalIDC, stats := range analysis.PhysicalIDCDistrib {
		ctx.SLog.Info("physical IDC long tail stats").
			Set("logic_idc", analysis.LogicIDC).
			Set("physical_idc", physicalIDC).
			Set("count", stats.Count).
			Set("percentage", fmt.Sprintf("%.2f%%", stats.Percentage)).
			Set("avg_latency", stats.AvgLatency).
			Set("max_latency", stats.MaxLatency).
			Set("gossip_groups", stats.GossipGroups).
			Set("ip_count", len(stats.IPs)).Print()
	}

	// 输出gossip组分布
	ctx.SLog.Info("=== Gossip Group Distribution ===").Set("logic_idc", analysis.LogicIDC).Print()
	for groupKey, stats := range analysis.GossipGroupDistrib {
		ctx.SLog.Info("gossip group long tail stats").
			Set("logic_idc", analysis.LogicIDC).
			Set("group_key", groupKey).
			Set("physical_idc", stats.PhysicalIDC).
			Set("group_index", stats.GroupIndex).
			Set("count", stats.Count).
			Set("percentage", fmt.Sprintf("%.2f%%", stats.Percentage)).
			Set("avg_latency", stats.AvgLatency).
			Set("max_latency", stats.MaxLatency).
			Set("group_size", stats.GroupSize).
			Set("long_tail_ratio", fmt.Sprintf("%.2f%%", float64(stats.Count)/float64(stats.GroupSize)*100)).Print()
	}
}

// generateOptimizationSuggestions 生成优化建议
func generateOptimizationSuggestions(ctx *easy.Context, analysis *LongTailAnalysis) *OptimizationSuggestion {
	if analysis == nil || analysis.CurrentConfig == nil {
		return nil
	}

	suggestion := &OptimizationSuggestion{
		LogicIDC:           analysis.LogicIDC,
		CurrentGroupNum:    analysis.CurrentConfig.GroupNum,
		PhysicalIDCChanges: make(map[string]int),
	}

	// 分析长尾分布特征
	highConcentrationThreshold := 0.3 // 如果某个物理IDC占长尾的30%以上，认为是高集中度

	var problematicIDCs []string
	var reasons []string

	// 检查物理IDC分布
	for physicalIDC, stats := range analysis.PhysicalIDCDistrib {
		concentration := stats.Percentage / 100.0

		if concentration > highConcentrationThreshold {
			problematicIDCs = append(problematicIDCs, physicalIDC)
			reasons = append(reasons, fmt.Sprintf("%s占长尾%.1f%%，集中度过高", physicalIDC, stats.Percentage))
		}
	}

	// 检查gossip组内长尾比例
	for groupKey, stats := range analysis.GossipGroupDistrib {
		if stats.GroupSize > 0 {
			longTailRatio := float64(stats.Count) / float64(stats.GroupSize)
			if longTailRatio > 0.2 { // 如果组内20%以上都是长尾，说明该组有问题
				reasons = append(reasons, fmt.Sprintf("组%s长尾比例%.1f%%过高", groupKey, longTailRatio*100))
			}
		}
	}

	// 生成建议
	if len(problematicIDCs) > 0 {
		// 建议增加问题物理IDC的组数
		for _, physicalIDC := range problematicIDCs {
			currentGroupNum := getGroupNumByIDC(*analysis.CurrentConfig, physicalIDC)
			suggestedIncrease := int(math.Ceil(float64(currentGroupNum) * 0.5)) // 增加50%
			suggestion.PhysicalIDCChanges[physicalIDC] = currentGroupNum + suggestedIncrease
		}

		suggestion.SuggestedGroupNum = analysis.CurrentConfig.GroupNum + 5 // 整体增加5个组
		suggestion.Reason = strings.Join(reasons, "; ")
		suggestion.ExpectedImprovement = "通过增加gossip组数，减少每组机器数量，降低长尾延迟集中度"
		suggestion.RiskAssessment = "低风险：增加组数会增加内存使用，但能有效分散负载"
	} else {
		// 没有明显问题
		suggestion.SuggestedGroupNum = analysis.CurrentConfig.GroupNum
		suggestion.Reason = "当前配置合理，长尾分布相对均匀"
		suggestion.ExpectedImprovement = "保持当前配置"
		suggestion.RiskAssessment = "无风险"
	}

	return suggestion
}

// outputOptimizationSuggestions 输出优化建议
func outputOptimizationSuggestions(ctx *easy.Context, suggestion *OptimizationSuggestion) {
	if suggestion == nil {
		return
	}

	ctx.SLog.Info("=== Optimization Suggestions ===").
		Set("logic_idc", suggestion.LogicIDC).
		Set("current_group_num", suggestion.CurrentGroupNum).
		Set("suggested_group_num", suggestion.SuggestedGroupNum).
		Set("reason", suggestion.Reason).
		Set("expected_improvement", suggestion.ExpectedImprovement).
		Set("risk_assessment", suggestion.RiskAssessment).Print()

	// 输出各物理IDC的建议变化
	if len(suggestion.PhysicalIDCChanges) > 0 {
		ctx.SLog.Info("=== Physical IDC Group Changes ===").Set("logic_idc", suggestion.LogicIDC).Print()
		for physicalIDC, suggestedGroupNum := range suggestion.PhysicalIDCChanges {
			ctx.SLog.Info("physical IDC group change suggestion").
				Set("logic_idc", suggestion.LogicIDC).
				Set("physical_idc", physicalIDC).
				Set("suggested_group_num", suggestedGroupNum).Print()
		}
	}
}
