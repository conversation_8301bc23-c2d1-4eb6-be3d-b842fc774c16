package main

import (
	"fmt"
	"log"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/gossiputil"
)

// 示例程序：演示如何使用物理IDC延迟分析功能
func main() {
	fmt.Println("=== 物理IDC延迟分析示例 ===")

	// 创建上下文
	ctx := easy.NewContext()

	// 执行物理IDC延迟分析
	fmt.Println("开始执行物理IDC延迟分析...")
	err := gossiputil.AnalysisPhysicalIDCLatency(ctx)
	if err != nil {
		log.Printf("执行物理IDC延迟分析时出错: %v", err)
		fmt.Println("注意：在实际环境中需要正确的Redis配置和数据")
	} else {
		fmt.Println("物理IDC延迟分析完成！")
	}

	fmt.Println("\n=== 分析结果说明 ===")
	fmt.Println("分析结果会输出到日志中，包含以下信息：")
	fmt.Println("- physical_idc: 物理机房名称（如 yq01, yq02, xaky 等）")
	fmt.Println("- metric_type: 指标类型（worker_latency, privileged_latency, total_latency, agent_latency）")
	fmt.Println("- p99, p95, p90, p80: 各百分位延迟值")
	fmt.Println("- total_count: 该物理IDC的总机器数")
	fmt.Println("- exceed_threshold_count: 超过阈值的机器数")
	fmt.Println("- gossip_group_num: 该物理IDC的gossip组数")
	fmt.Println("- timestamp: 统计时间戳")

	fmt.Println("\n=== 支持的物理IDC ===")
	physicalIDCs := []string{
		"yq01", "yq02", // 阳泉机房
		"xaky", "xakd", "xafj", // 西安机房
		"bjhw", "bdjl", "bjyz", "bddwd", "bddx", "gajl", // 北京机房
	}

	for _, idc := range physicalIDCs {
		fmt.Printf("- %s\n", idc)
	}

	fmt.Println("\n=== 使用说明 ===")
	fmt.Println("1. 该功能会从全局延迟数据中读取所有IP的延迟信息")
	fmt.Println("2. 通过IP反查hostname，获取物理IDC信息")
	fmt.Println("3. 按物理IDC分组统计P99、P95、P90等延迟百分位")
	fmt.Println("4. 结合gossip配置信息，展示每个物理IDC的组数")
	fmt.Println("5. 支持worker_latency、privileged_latency、total_latency、agent_latency四种延迟类型")

	fmt.Println("\n=== 长尾分析功能 ===")
	fmt.Println("开始执行长尾分析...")

	// 执行长尾分析，分析最后100个长尾数据
	err = gossiputil.AnalysisLongTailResult(ctx, 100)
	if err != nil {
		log.Printf("执行长尾分析时出错: %v", err)
		fmt.Println("注意：在实际环境中需要正确的Redis配置和数据")
	} else {
		fmt.Println("长尾分析完成！")
	}

	fmt.Println("\n=== 长尾分析说明 ===")
	fmt.Println("长尾分析会输出以下信息：")
	fmt.Println("1. 延迟特征：最小值、最大值、平均值、中位数、P95、P99、标准差")
	fmt.Println("2. 物理IDC分布：各物理IDC的长尾数量、占比、平均延迟、最大延迟")
	fmt.Println("3. Gossip组分布：各组的长尾数量、占比、组内长尾比例")
	fmt.Println("4. 优化建议：基于分析结果提供的组数调整建议")

	fmt.Println("\n=== 优化策略 ===")
	fmt.Println("1. 如果某个物理IDC长尾集中度过高（>30%），建议增加该IDC的组数")
	fmt.Println("2. 如果某个gossip组内长尾比例过高（>20%），说明该组负载不均")
	fmt.Println("3. 通过增加组数可以减少每组机器数量，分散负载，降低长尾延迟")
	fmt.Println("4. 需要权衡组数增加带来的内存开销和延迟改善的收益")
}
