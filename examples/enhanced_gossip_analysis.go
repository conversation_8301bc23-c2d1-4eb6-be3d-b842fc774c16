package main

import (
	"fmt"
	"log"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/gossiputil"
)

func main() {
	// 创建上下文
	ctx := easy.NewContext()

	fmt.Println("=== Enhanced Gossip Protocol Long Tail Analysis ===")
	fmt.Println()

	fmt.Println("这个增强版的长尾分析工具提供了以下新功能：")
	fmt.Println()

	fmt.Println("1. 长尾机器在gossip组索引上的分布分析")
	fmt.Println("   - 统计每个索引的长尾数量和比例")
	fmt.Println("   - 识别问题索引（长尾比例过高的索引）")
	fmt.Println("   - 分析索引与物理IDC的关联性")
	fmt.Println()

	fmt.Println("2. 不同gossip组之间的延迟差异分析")
	fmt.Println("   - 计算各组的平均延迟、最大延迟、最小延迟")
	fmt.Println("   - 分析组大小与长尾比例的相关性")
	fmt.Println("   - 识别异常组和性能瓶颈")
	fmt.Println("   - 跨物理IDC的延迟对比")
	fmt.Println()

	fmt.Println("3. 与AnalysisPhysicalIDCLatency的结合分析")
	fmt.Println("   - 关联长尾分布与基准延迟数据")
	fmt.Println("   - 分析gossip配置与性能的关联性")
	fmt.Println("   - 评估组密度对长尾比例的影响")
	fmt.Println()

	fmt.Println("4. 增强的gossip协议健康度分析")
	fmt.Println("   - gossip组健康度评估")
	fmt.Println("   - 负载均衡情况分析")
	fmt.Println("   - 延迟分布模式识别")
	fmt.Println()

	fmt.Println("5. 跨IDC对比分析和最佳实践识别")
	fmt.Println("   - 跨逻辑IDC的性能对比")
	fmt.Println("   - 识别最佳配置实践")
	fmt.Println("   - 提供基于最佳实践的改进建议")
	fmt.Println()

	fmt.Println("=== 分析输出示例 ===")
	fmt.Println()

	fmt.Println("长尾机器索引分布分析：")
	fmt.Println("- gossip index 0: 长尾数量 15, 占比 25.00%, 平均延迟 1250.5ms")
	fmt.Println("- gossip index 1: 长尾数量 8, 占比 13.33%, 平均延迟 980.2ms")
	fmt.Println("- 问题索引检测: index 0 长尾比例过高 (25.00%)")
	fmt.Println()

	fmt.Println("gossip组延迟差异分析：")
	fmt.Println("- 物理IDC yq01: 最小平均延迟 850ms, 最大平均延迟 1350ms, 延迟范围 500ms")
	fmt.Println("- 组大小与长尾比例相关系数: 0.65 (中等相关)")
	fmt.Println("- 异常组检测: yq01-0 平均延迟 1350ms 超过阈值 1200ms")
	fmt.Println()

	fmt.Println("与物理IDC延迟的关联分析：")
	fmt.Println("- 物理IDC yq01: 长尾比例 15.5%, 基准超阈值比例 8.2%, 相关性: 高相关")
	fmt.Println("- 组密度分析: yq01 组密度 245.3 (过高), 建议增加组数")
	fmt.Println()

	fmt.Println("跨IDC性能对比：")
	fmt.Println("- 最佳IDC: yangquan, 平均长尾比例 8.5%")
	fmt.Println("- 最差IDC: beijing, 平均长尾比例 18.2%")
	fmt.Println("- 性能差距: 9.7%")
	fmt.Println()

	fmt.Println("=== 执行分析 ===")
	fmt.Println()

	// 执行增强的长尾分析，分析最后100个长尾数据
	err := gossiputil.AnalysisLongTailResult(ctx, 100)
	if err != nil {
		log.Printf("执行增强长尾分析时出错: %v", err)
		fmt.Println("注意：在实际环境中需要正确的Redis配置和数据")
	} else {
		fmt.Println("增强长尾分析完成！")
	}

	fmt.Println()
	fmt.Println("=== 分析结果说明 ===")
	fmt.Println()

	fmt.Println("通过这些增强的分析功能，你可以：")
	fmt.Println("1. 精确定位长尾问题是否集中在特定的gossip组索引")
	fmt.Println("2. 了解不同gossip组之间的性能差异和相关性")
	fmt.Println("3. 结合基准延迟数据，更准确地评估gossip配置的合理性")
	fmt.Println("4. 通过跨IDC对比，学习最佳配置实践")
	fmt.Println("5. 获得具体的优化建议，如调整组数、优化组密度等")
	fmt.Println()

	fmt.Println("这些分析结果将帮助你更好地理解gossip协议的性能特征，")
	fmt.Println("并为优化gossip配置提供数据支持。")
}
